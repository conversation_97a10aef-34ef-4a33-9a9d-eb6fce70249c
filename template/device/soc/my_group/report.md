## soc 部分 自主编译部分
### 添加共享库
例如如果你定义了rga作为共享库,那么你可以在rga的目录下的**BUILD.gn**中这样定义配置
```
import("//build/ohos.gni")
import("//build/ohos/ndk/ndk.gni")

ohos_prebuilt_shared_library("librga") {
  if (target_cpu == "arm") {
    source = "lib/librga.z.so"//修改为你rga目录下的存放路径
  } else if (target_cpu == "arm64") {
    source = "lib64/librga.z.so"
  }
  innerapi_tags = [ "passthrough_indirect" ]
  install_images = [ chipset_base_dir ]
  subsystem_name = "rockchip_products"
  part_name = "rockchip_products"
  install_enable = true
}
```
相应的,你还需要在rga下有**include目录**存放相关头文件
然后你可以**在其他BUILD.gn中引用它**
```
  include_dirs = [
    "//device/soc/rockchip/rk3568/hardware/rga/include",# if you use rga
  ]

  deps = [
    #目录:库名称
    "//device/soc/rockchip/rk3568/hardware/rga:librga",# if you use rga
  ]
  
```
### 添加group编译
如果你需要添加自己的group编译,例如希望编译mpp,那么你也可以在hardware目录下添加所需目录
目录格式参照如下
```
├── include //存放头文件
├── lib  //存放共享库文件
├── lib64 
├── mpp // 其他库，对libmpp无影响
│   ├── hdi_mpp 
│   └── legacy
└── src //存放源码
```
在BUILD.gn下进行配置
```
import("//build/ohos.gni")
import("//build/ohos/ndk/ndk.gni")

#定义库
ohos_prebuilt_shared_library("libmpp") {
  if (target_cpu == "arm") {
    source = "lib/librockchip_mpp.z.so"//添加共享库
  } else {
    source = "lib64/librockchip_mpp.z.so"
  }
  innerapi_tags = [ "passthrough_indirect" ]
  install_images = [ chipset_base_dir ]
  part_name = "rockchip_products"
  install_enable = true
}

group("mpp") {
  deps = [ ":libmpp" ]#添加库
}
```
还可以参考源码集 + 动态库的形式:
ohos_source_set 本身不直接生成库文件或可执行文件,它只是一个“源码编译规则”的集合.
ohos_shared_library 找到 rockchip_vpu_src 编译出来的所有目标文件，然后将它们链接（link）成一个最终的 librockchip_vpu.so 动态库文件。
```
import("//build/ohos.gni")

ohos_source_set("rockchip_vpu_src") {
  sources = [
    "rk_list.cpp",
    "vpu.c",
    "vpu_api.cpp",
    "vpu_api_legacy.cpp",
    "vpu_api_mlvec.cpp",
    "vpu_mem_legacy.c",
  ]

  cflags_c = [
    "-Wall",
    "-Wextra",
    "-Werror",
    "-Wno-format",
  ]

  cflags_cc = [
    "-Wall",
    "-Wextra",
    "-Werror",
    "-Wno-predefined-identifier-outside-function",
    "-Wno-macro-redefined",
    "-Wno-format-extra-args",
    "-Wno-format",
    "-DHAVE_CONFIG_H",
    "-DMPP_VERSION=\"1.3.7\"",
    "-DMPP_VER_HIST_CNT=0",
    "-DMPP_VER_HIST_0=\"version_0\"",
    "-DMPP_VER_HIST_1=\"version_1\"",
    "-DMPP_VER_HIST_2=\"version_2\"",
    "-DMPP_VER_HIST_3=\"version_3\"",
    "-DMPP_VER_HIST_4=\"version_4\"",
    "-DMPP_VER_HIST_5=\"version_5\"",
    "-DMPP_VER_HIST_6=\"version_6\"",
    "-DMPP_VER_HIST_7=\"version_7\"",
    "-DMPP_VER_HIST_8=\"version_8\"",
    "-DMPP_VER_HIST_9=\"version_9\"",
  ]

  include_dirs = [
    "./inc",
    "//device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp",
    "//device/soc/rockchip/rk3568/hardware/mpp/include",
    "//device/soc/rockchip/rk3568/hardware/mpp/include",
    "//commonlibrary/c_utils/base/include",
  ]

  deps = [
    "//base/hiviewdfx/hilog/interfaces/native/innerkits:libhilog",
    "//device/soc/rockchip/rk3568/hardware/mpp/mpp/hdi_mpp:hdi_mpp",
  ]
  external_deps = [
    "c_utils:utils",
    "hdf_core:libhdf_utils",
  ]
}

ohos_shared_library("librockchip_vpu") {
  deps = [ ":rockchip_vpu_src" ]
  innerapi_tags = [ "passthrough_indirect" ]
  install_images = [ chipset_base_dir ]
  subsystem_name = "hdf"
  part_name = "rockchip_products"
}
```