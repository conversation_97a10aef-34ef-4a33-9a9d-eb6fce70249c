## OMX IL 组件
1. 配置文件修改
   - 修改BUILD.gn中相关库依赖
   - 修改 rk_omx.gni，根据您的SoC能力调整功能开关：
2. OSAL层适配:重命名和修改核心文件:omx_il\osal
3. 基础组件适配:omx_il\component
4. 硬件抽象层适配：如果如果您的SoC没有类似MPP的媒体处理框架，需要：
    - 替换MPP相关头文件引用
    - 实现您自己的硬件编解码器接口
    - 修改mpp\include\mpp_soc.h中的SoC类型定义
5. 编解码器组件适配：修改omx_il\component\video\enc\BUILD.gn中的编译选项：
   ```
    # 根据您的SoC能力调整定义
    if (USE_YOUR_SOC_GPU) {
        defines += [ "YOUR_SOC_GPU_LIB_ENABLE" ]
    }

    if (USE_YOUR_SOC_HEVC_ENC) {
        defines += [ "SUPPORT_YOUR_SOC_HEVC_ENC" ]
    }
   ```
6. 核心组件适配：修改omx_il\core目录下文件


