## display
display是系统必要的组件，display在RK系列芯片的实现中可能涉及librga库。如果不需要librga库，可参考qemu虚拟机display的实现，文档链接：https://gitee.com/open-harmony-edu-dist/device_soc_edu
以下是目录结构
```
├── include
└── src
    ├── display_device
    ├── display_gfx
    └── display_gralloc
```
display涉及到的修改主要分为**display_gralloc**和**display_device**两部分。
### display_gralloc
#### display_buffer_vdi_impl.cpp
修改DisplayBufferVdiImpl函数
#### display_gralloc_gbm.cpp
1. 修改GbmGrallocInitialize函数
2. const char *g_drmFileNode路径
3. pixelStrMaps、formatStrMaps和convertTable按需修改
#### hi_gbm.cpp   
1. struct gbm_bo *hdi_gbm_bo_create(struct gbm_device *gbm, uint32_t width, uint32_t height, uint32_t format,
uint32_t usage)
dumb.flags = ROCKCHIP_BO_DMA32;按需修改
2. static const FormatInfo *GetFormatInfo(uint32_t format)中static const FormatInfo fmtInfos[]按需修改
### display_device
#### display_gfx.c
**具体去实现display_gfx.h结构体中的各个函数**。
```
int32_t GfxInitialize(GfxFuncs **funcs)
{
    DISPLAY_CHK_RETURN((funcs == NULL), DISPLAY_PARAM_ERR, DISPLAY_LOGE("info is null"));
    GfxFuncs *gfxFuncs = (GfxFuncs *)malloc(sizeof(GfxFuncs));
    DISPLAY_CHK_RETURN((gfxFuncs == NULL), DISPLAY_NULL_PTR, DISPLAY_LOGE("gfxFuncs is nullptr"));
    errno_t eok = memset_s((void *)gfxFuncs, sizeof(GfxFuncs), 0, sizeof(GfxFuncs));
    if (eok != EOK) {
        DISPLAY_LOGE("memset_s failed");
        free(gfxFuncs);
        return DISPLAY_FAILURE;
    }
    gfxFuncs->InitGfx = rkInitGfx;//这些函数都是要去填充的
    gfxFuncs->DeinitGfx = rkDeinitGfx;
    gfxFuncs->FillRect = rkFillRect;
    gfxFuncs->Blit = rkBlit;
    gfxFuncs->Sync = rkSync;
    *funcs = gfxFuncs;

    return DISPLAY_SUCCESS;
}
```
#### drm_connector.c
1. SetBrightness函数需要修改，针对亮度等级修改
   `bytes = sprintf_s(buffer, sizeof(buffer), "%d", light_level);//light_level针对具体soc作修改、限制`
2. void DrmConnector::ConvertTypeToName(uint32_t type, std::string &name)增加 `case`条件
3. void DrmConnector::ConvertToHdiType(uint32_t type, InterfaceType &hdiType)增加 `case`条件
#### drm_crtc
可能需要增加对于plane mask的支持
定义变量时可能增加mPlaneMask，并可被外部通过uint32_t GetPlaneMask()获取
需要对int32_t DrmCrtc::Init(DrmDevice &drmDevice)修改
#### drm_device
rk系列在GetProperty中多了完整解析和存储 DRM 属性的所有元数据信息
1. 修改int32_t DrmDevice::GetProperty(uint32_t objId, uint32_t objType, const std::string &name, DrmProperty &prop)
2. **convertTable**相应修改
3. DrmDevice::Create()更改函数内部的log时的name
#### drm_display
1. 修改convertTable后，int32_t DrmDisplay::PushFirstFrame()的const AllocInfo info的format需要相应修改
2. SetDisplayPowerStatus需要修改，可以通过调用mConnector对象的SetDrmPowerState方法来设置。rk3568通过open和ioctl系统调用直接操作设备文件/dev/graphics/fb0
3. int32_t DrmDisplay::WaitForVBlank(uint64_t *ns)需要修改，有两种实现方式用usleep固定等待和利用DRM_VBLANK实现阻塞等待内核DRM驱动发出的VBlank硬件信号。
#### drm_plane
1. `struct PlaneTypeName planeTypeNames[]`内部的修改和存在，如果没有该结构体那么还需要对int32_t DrmPlane::Init(DrmDevice &drmDevice)进行修改，去除有关部分

    **识别和分类 Rockchip {{soc}} 显示控制器中每个硬件平面（Plane）的具体类型和能力**
2. 头文件中也包含了对`planeTypeNames`相关的定义，需要相应修改
#### drm_vsync_worker
uint64_t DrmVsyncWorker::WaitNextVBlank(unsigned int &sq)需要修改，这与drm_display部分是类似的，是选择机械的等待还是根据硬件信号等待
#### hdi_display
1. PrepareDisplayLayers需要修改，主要是对mClientLayer->SetLayerZorder(topZpos);的修改
   是否启用mClientLayer->SetLayerZorder(topZpos);和topZpos的值

#### hdi_drm_composition
1. int32_t HdiDrmComposition::ApplyPlane(HdiDrmLayer &layer,
                                          HdiLayer &hlayer,
                                          DrmPlane &drmPlane,
                                          drmModeAtomicReqPtr pset)

    virt在上一文件**删除了设置SetLayerZorder**，那么在这里相应的删除一部分代码
    ```c
        ret = drmModeAtomicAddProperty(pset, drmPlane.GetId(), drmPlane.GetPropZposId(), layer.GetZorder());
        DISPLAY_LOGD("set the fb planeid %{public}d, GetPropZposId %{public}d, zpos %{public}d",
            drmPlane.GetId(), drmPlane.GetPropZposId(), layer.GetZorder());
        DISPLAY_CHK_RETURN((ret < 0), DISPLAY_FAILURE, DISPLAY_LOGE("set the zpos fialed errno : %{public}d", errno));
    ```
2. int32_t HdiDrmComposition::RemoveUnusePlane(drmModeAtomicReqPtr pset)简化和复杂实现
    1. ```c
        if ((static_cast<int>(drmPlane->GetWinType()) & mCrtc->GetPlaneMask()) &&  drmPlane->GetPipe() == 0) {
        ……
        //简化实现，不依赖drmPlane->GetWinType()和mCrtc->GetPlaneMask()
                if (drmPlane->GetPipe() == 0) {
        ……
        ```
3. int32_t HdiDrmComposition::FindPlaneAndApply(drmModeAtomicReqPtr pset)

    ```c
    //简化实现，不依赖drmPlane->GetWinType()和mCrtc->GetPlaneMask()，则下面这段代码是没有的
                
    /* Check whether the plane belond to the crtc */
                if (!(static_cast<int>(drmPlane->GetWinType()) & mCrtc->GetPlaneMask())) {
                    continue;
                }
                DISPLAY_LOGD("use plane %{public}d WinType %{public}x crtc %{public}d PlaneMask %{public}x",
                    drmPlane->GetId(), drmPlane->GetWinType(), mCrtc->GetId(), mCrtc->GetPlaneMask());
    ```

    spacemit-k1增加了DMA的静态绑定，可能需要注意

    ```c
                if (j == 0) {
                    drmPlane->BindToRdma(DPU_RDMA_0);
                } else if (j == 1) {
                    drmPlane->BindToRdma(DPU_RDMA_1);
                } else if (j == 2) {
                    drmPlane->BindToRdma(DPU_RDMA_2);
                } else if (j == 3) {
                    drmPlane->BindToRdma(DPU_RDMA_3);
                } else {
                    drmPlane->BindToRdma(DPU_RDMA_INVALID);
    ```
#### hdi_drm_layer
1. void DrmGemBuffer::Init(int drmFd, HdiLayerBuffer &hdl)根据**`convertTable`**  **可能需要**相应作修改mDrmFormat
    ```c
    ret = drmModeAddFB2(drmFd, hdl.GetWight(), hdl.GetHeight(), mDrmFormat, gemHandles, pitches, offsets, &mFdId, 0);
    ```
2.. `GetFileName` 和 `DumpLayerBuffer`是否启用，如果不启用则注释掉相关代码，例如

    ```c
        mAcquireFence = dup(fence);
        // if (access("/data/hdi_dump_layer", F_OK) != -1) {
        //     if (DumpLayerBuffer(const_cast<BufferHandle *>(buffer)) != DISPLAY_SUCCESS) {
        //         DISPLAY_LOGE("dump layer buffer failed");
        //     }
        // }
        return DISPLAY_SUCCESS;
    ```

#### hdi_gfx_composition

1. bool HdiGfxComposition::UseCompositionClient(std::vector<HdiLayer *> &layers)
    - bool hasCompositionClient = false;（true）
    - layer->SetDeviceSelect(defaultCompType);(增删)