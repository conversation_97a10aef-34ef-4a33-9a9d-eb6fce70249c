# Copyright (C) 2021 - 2023 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//build/ohos/ndk/ndk.gni")

ohos_prebuilt_shared_library("librga") {
  if (target_cpu == "arm") {
    source = "lib/librga.z.so"
  } else if (target_cpu == "arm64") {
    source = "lib64/librga.z.so"
  }
  innerapi_tags = [ "passthrough_indirect" ]
  install_images = [ chipset_base_dir ]
  subsystem_name = "{{ soc_company }}_products"
  part_name = "{{ soc_company }}_products"
  install_enable = true
}
