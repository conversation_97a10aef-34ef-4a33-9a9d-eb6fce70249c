## kernel 模块
在输出的kernel目录下你可能需要存放相应的开机logo图片
命名为logo.bmp和logo_kernel.bmp

- 你可以把你自己的deconfig文件存放在kernel/config目录下.
build_kernel.sh的执行顺序是
1. 相关变量定义和环境变量设置
2. 对kernel部分打patch
3. 根据需要对deconfig进行合并，最终需要拷贝产品的deconfig文件到${KERNEL_SRC_TMP_PATH}/arch/arm64/configs/rockchip_linux_defconfig下，这里的rockchip_linux_defconfig目录可以根据你的产品名称进行命名
4. 执行./make-ohos.sh从源代码生成所有必要的文件，并把它们整理好放在 boot_linux 文件夹里。
5. 执行./make-boot.sh将一个已编译好的内核文件夹打包成一个可引导的 boot_linux.img 磁盘镜像文件
你可能需要大幅修改build_kernel.sh以及相关文件中的内容，目前代码对于rk3568进行适配，如果是其他开发板请按照以下步骤适配。
1. 修改deconfig文件
2. 修改过的linux内核或者采用patch形式（这将包含非常多的修改）
3. 修改kernel/make-ohos.sh中的model_list确保包含你所开发的产品