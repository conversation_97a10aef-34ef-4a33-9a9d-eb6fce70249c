## <PERSON><PERSON> Modules
Within the output kernel directory, you may need to store the corresponding boot logo images.
Name them logo.bmp and logo_kernel.bmp.

- You may store your own decconfig file in the kernel/config directory.
The execution sequence of build_kernel.sh is:
1. Definition of relevant variables and setting of environment variables
2. Apply patches to the kernel section
3. Merge decconfig files as required. The final product's decconfig file must be copied to ${KERNEL_SRC_TMP_PATH}/arch/arm64/configs/rockchip_linux_defconfig. The rockchip_linux_defconfig directory may be renamed according to your product's designation
4. Execute ./make-ohos.sh to generate all necessary files from source code, organising them into the boot_linux folder.
5. Execute ./make-boot.sh to package a compiled kernel folder into a bootable boot_linux.img disk image file.
You may need to substantially modify build_kernel.sh and related files. The current code is adapted for rk3568; for other development boards, follow these adaptation steps:
1. Modify the decfig file
2. Modify the Linux kernel or apply patches (this will involve extensive modifications)
3. Modify the model_list in kernel/make-ohos.sh to ensure it includes your product model