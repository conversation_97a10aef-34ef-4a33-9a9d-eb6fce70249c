#!/bin/bash

# Updater configuration files list
COPY_FILES=(
    "updater_ramdisk_resource_config.ini"
)

# Config directory files to copy
CONFIG_FILES=(
    "fstab.updater"
    "init.cfg"
    "init.rk3568.usb.cfg"
    "lastword.sh"
    "signing_cert.crt"
)

# Check environment variables
if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "Error: TEMPLATE_OHOS_PATH environment variable not set"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "Error: TEMPLATE_PRODUCT_NAME environment variable not set"
    exit 1
fi

# Source directory path - updater configuration files source directory
SOURCE_BASE_DIR="$TEMPLATE_OHOS_PATH/device/board/hihope/rk3568/updater"

# Check if source base directory exists
if [ ! -d "$SOURCE_BASE_DIR" ]; then
    echo "Warning: Updater source directory not found, attempted path: $SOURCE_BASE_DIR"
    echo "Skipping file copy"
    exit 0
fi

# 目标目录（输出目录下对应的位置）
# 构建输出目录路径：output/{ohos_version}/{soc_company}/{soc}/display
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0"
fi

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    # 脚本路径: template/device/soc/display/copy_file.sh
    # 需要向上4级到达项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# Build target directory path - 新的目录结构: board/hihope/rk3568/updater
if [ -z "$TEMPLATE_DEVICE_COMPANY" ]; then
    TEMPLATE_DEVICE_COMPANY="hihope"  # 默认设备厂商
fi
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/board/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/updater"

# Ensure target directory exists
mkdir -p "$TARGET_DIR"
mkdir -p "$TARGET_DIR/config"

echo "Target directory: $TARGET_DIR"
echo "Copying updater configuration files from $SOURCE_BASE_DIR to $TARGET_DIR"

# Copy specified configuration files
copied_count=0
for file in "${COPY_FILES[@]}"; do
    source_file="$SOURCE_BASE_DIR/$file"
    target_file="$TARGET_DIR/$file"

    if [ -f "$source_file" ]; then
        echo "Copying configuration file: $file"
        # Copy file
        cp "$source_file" "$target_file" || {
            echo "Warning: Error copying file $file"
        }
        ((copied_count++))
    else
        echo "Warning: Source file does not exist: $source_file"
    fi
done

# Copy config directory files
for file in "${CONFIG_FILES[@]}"; do
    source_file="$SOURCE_BASE_DIR/config/$file"
    target_file="$TARGET_DIR/config/$file"

    if [ -f "$source_file" ]; then
        echo "Copying config file: config/$file"
        # Copy file
        cp "$source_file" "$target_file" || {
            echo "Warning: Error copying config file $file"
        }

        # Special handling for init.rk3568.usb.cfg - rename rk3568 to current SoC name
        if [[ "$file" == "init.rk3568.usb.cfg" ]]; then
            if [ -n "$TEMPLATE_SOC" ]; then
                new_filename="init.${TEMPLATE_SOC}.usb.cfg"
                # Only rename if the target filename is different from source filename
                if [[ "$file" != "$new_filename" ]]; then
                    new_target_file="$TARGET_DIR/config/$new_filename"
                    echo "Renaming $file to $new_filename for current SoC: $TEMPLATE_SOC"
                    mv "$target_file" "$new_target_file" || {
                        echo "Warning: Error renaming $file to $new_filename"
                    }
                else
                    echo "SoC name matches source filename ($TEMPLATE_SOC), no renaming needed"
                fi
            else
                echo "Warning: TEMPLATE_SOC not set, keeping original filename: $file"
            fi
        fi

        ((copied_count++))
    else
        echo "Warning: Source config file does not exist: $source_file"
    fi
done

if [ $copied_count -gt 0 ]; then
    echo "Updater configuration files copy completed, successfully copied $copied_count files"
else
    echo "Warning: No specified configuration files found"
    echo "Creating empty updater directory as placeholder"
fi

echo "Script execution completed, target directory: $TARGET_DIR"
