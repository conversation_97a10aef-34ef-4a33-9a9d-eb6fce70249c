## Camera 摄像头模块
### device_manager
1. 修改camera/vdi_impl/v4l2/device_manager/src/{sensor}.cpp，sensor为具体的传感器名称
2. 在{sensor}.cpp根据需要添加其他**相机传感器初始化函数**。
### pipeline_core
这部分主要是对自定义节点进行代码修改，为了简化开发，用户可以在yaml文件中指定需要添加的自定义节点名称，camera/vdi_impl/v4l2/pipeline_core/src下会生成相应名称的.cpp和.h文件。用户根据需要自行修改文件中的内容。

### 对于使用USB摄像头的说明
当在yaml配置文件中设置camera_drivers的method为 usb 时，将会替换ohos.build中的camera构建方式，board目录下的camera中文件是关于v4l2框架的，因此该目录会失效。