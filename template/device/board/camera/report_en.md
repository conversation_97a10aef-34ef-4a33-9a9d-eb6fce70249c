## Camera Module
### device_manager
1. Modify camera/vdi_impl/v4l2/device_manager/src/{sensor}.cpp, where sensor denotes the specific sensor name.
2. Add additional **camera sensor initialisation functions** as required within {sensor}.cpp.
### pipeline_core
This section primarily involves modifying code for custom nodes. To simplify development, users may specify the desired custom node name within the YAML file. Corresponding .cpp and .h files will be generated under camera/vdi_impl/v4l2/pipeline_core/src. Users should modify the contents of these files as required.
