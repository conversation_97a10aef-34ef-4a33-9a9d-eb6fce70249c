#!/bin/bash

# 分布式硬件配置文件列表
COPY_FILES=(
    "dinput_business_event_whitelist.cfg"
    "distributed_hardware_components_cfg.json"
)

# 检查环境变量
if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "错误: TEMPLATE_OHOS_PATH 环境变量未设置"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "错误: TEMPLATE_PRODUCT_NAME 环境变量未设置"
    exit 1
fi

# Source directory path - 分布式硬件配置文件源目录
SOURCE_BASE_DIR="$TEMPLATE_OHOS_PATH/device/board/hihope/rk3568/distributedhardware"

# 检查源基础目录是否存在
if [ ! -d "$SOURCE_BASE_DIR" ]; then
    echo "警告: 未找到分布式硬件源目录，尝试的路径: $SOURCE_BASE_DIR"
    echo "跳过文件复制"
    exit 0
fi

# 目标目录（输出目录下对应的位置）
# 构建输出目录路径：output/{ohos_version}/{soc_company}/{soc}/display
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0"
fi

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    # 脚本路径: template/device/soc/display/copy_file.sh
    # 需要向上4级到达项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# 构建目标目录路径 - 新的目录结构: board/hihope/rk3568/distributedhardware
if [ -z "$TEMPLATE_DEVICE_COMPANY" ]; then
    TEMPLATE_DEVICE_COMPANY="hihope"  # 默认设备厂商
fi
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/board/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/distributedhardware"

# 确保目标目录存在
mkdir -p "$TARGET_DIR"

echo "目标目录: $TARGET_DIR"
echo "从 $SOURCE_BASE_DIR 复制分布式硬件配置文件到 $TARGET_DIR"

# 复制指定的配置文件
copied_count=0
for file in "${COPY_FILES[@]}"; do
    source_file="$SOURCE_BASE_DIR/$file"
    target_file="$TARGET_DIR/$file"

    if [ -f "$source_file" ]; then
        echo "复制配置文件: $file"
        # 复制文件
        cp "$source_file" "$target_file" || {
            echo "警告: 复制文件 $file 时出现错误"
        }
        ((copied_count++))
    else
        echo "警告: 源文件不存在: $source_file"
    fi
done

if [ $copied_count -gt 0 ]; then
    echo "分布式硬件配置文件复制完成，成功复制 $copied_count 个文件"
else
    echo "警告: 没有找到任何指定的配置文件"
    echo "创建空的distributedhardware目录作为占位符"
fi

echo "脚本执行完成，目标目录: $TARGET_DIR"
