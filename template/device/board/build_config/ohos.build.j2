{
    "subsystem": "{{ soc_company }}_products",
    "parts": {
        "{{ soc_company }}_products": {
            "module_list": [
                "//vendor/{{ product_company }}/{{ product_name }}/bluetooth:libbt_vendor",
                "//vendor/{{ product_company }}/{{ product_name }}/bluetooth:BCM4362A2.hcd",
{%- if is_usb_camera %}
                "//drivers/peripheral/camera/vdi_base/usb_camera:usb_camera_vdi_impl",
{%- else %}
                "//device/board/{{ product_company }}/{{ device_name }}/camera/vdi_impl/v4l2:camera_board_vdi_impl",
{%- endif %}
                "//device/board/{{ product_company }}/{{ product_name }}:{{ product_name }}_group"
            ]
        }
    }
}
