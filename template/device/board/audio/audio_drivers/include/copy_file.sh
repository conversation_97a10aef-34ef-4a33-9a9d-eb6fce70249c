#!/bin/bash

# ==============================================================================
# 脚本说明:
# 从环境变量 TEMPLATE_OHOS_PATH 指定的源目录中，
# 复制 FILES_TO_COPY 数组中指定的特定文件到产品输出目录。
# ==============================================================================

# --- 1. 配置区域 ---

# 在此数组中定义需要复制的文件名列表
# 例如: FILES_TO_COPY=("file1.h" "file2.c" "subdir/file3.h")
FILES_TO_COPY=(
    "audio_device_log.h"
    # 在这里添加其他需要复制的文件名
)

# --- 2. 检查环境变量 ---

if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "错误: 环境变量 TEMPLATE_OHOS_PATH 未设置。"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "错误: 环境变量 TEMPLATE_PRODUCT_NAME 未设置。"
    exit 1
fi

# 如果OHOS版本变量未设置，提供一个默认值
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0" #可以根据需要修改默认版本
fi

# --- 3. 定义源目录和目标目录 ---

# 源目录的基础路径 (以rk3568为模板)
# 请根据你的实际OpenHarmony目录结构调整此路径
SOURCE_BASE_DIR="$TEMPLATE_OHOS_PATH/device/board/hihope/rk3568/audio_drivers/include"

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    # 脚本路径: template/device/board/audio/audio_drivers/include/copy_file.sh
    # 需要向上6级到达项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# 构建目标目录的完整路径 - 新的目录结构: board/hihope/rk3568/audio_drivers/include
if [ -z "$TEMPLATE_DEVICE_COMPANY" ]; then
    TEMPLATE_DEVICE_COMPANY="hihope"  # 默认设备厂商
fi
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/board/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/audio_drivers/include"


# --- 4. 执行复制操作 ---

echo "源目录: $SOURCE_BASE_DIR"
echo "目标目录: $TARGET_DIR"

# 检查源目录是否存在
if [ ! -d "$SOURCE_BASE_DIR" ]; then
    echo "错误: 源目录不存在，无法复制任何文件。"
    echo "尝试的路径: $SOURCE_BASE_DIR"
    exit 1
fi

# 确保目标目录存在，如果不存在则创建
mkdir -p "$TARGET_DIR"

echo "开始复制指定文件..."

# 遍历文件列表并执行复制
copied_count=0
skipped_count=0
for file_to_copy in "${FILES_TO_COPY[@]}"; do
    source_file_path="$SOURCE_BASE_DIR/$file_to_copy"
    
    # 检查源文件是否存在
    if [ -f "$source_file_path" ]; then
        echo "  -> 正在复制: $file_to_copy"
        # 复制文件到目标目录
        cp "$source_file_path" "$TARGET_DIR/"
        copied_count=$((copied_count + 1))
    else
        echo "  -> 警告: 源文件不存在，已跳过: $source_file_path"
        skipped_count=$((skipped_count + 1))
    fi
done

echo "----------------------------------------"
echo "脚本执行完成。"
echo "成功复制 $copied_count 个文件。"
if [ $skipped_count -gt 0 ]; then
    echo "跳过 $skipped_count 个不存在的文件。"
fi
echo "所有文件已复制到: $TARGET_DIR"
echo "----------------------------------------"