## Audio 音频模块

下面文档以{codec_chip}_codec为例进行说明，涉及具体编解码芯片名称在代码中已进行替换。
<codec_chip>和<compatible_driver>在yaml中的定义是为了兼容rk3568的设计，如果选用的编解码芯片不存在

ADM框架的音频驱动主要分为codec、dai、soc(dma)、dsp四个部分，分别需要对codec 、dai、soc(dma)部分进行修改
根据hdf框架要求，首先需要在adapter文件中实现具体驱动的注册，而底层驱动的控制由linux内核的驱动框架实现，
因此需要有相应的linux_driver.c文件进行linux设备驱动注册，
impl.c文件则是对于adapter中注册的操作数的具体实现文件。
- codec目录下的文件主要是对于具体codec芯片的操作实现。
- dai目录下的文件主要是对于dai总线的操作实现。
- soc目录下的文件主要是对于soc相关的dma操作实现。
- dsp目录目前没有具体实现，仅有框架，函数内部无实际操作。

### 开发者需要完成的工作:
#### codec部分
codec include中的头文件应按照具体芯片手册内容更改头文件。
例如{compatible_driver}_codec.h中定义了寄存器地址、寄存器位域等信息，
{codec_chip}_codec_impl.h中定义了可能用到的结构体以及{codec_chip}_codec_impl.c中函数对外声明。

在{product_name}/audio_drivers/codec/{codec_chip}_codec/src/{codec_chip}_codec_adapter.c中定义了私有数据结构和操作（注册回调函数），完成了hdf驱动节点的注册。
定义了三个私有结构体g_{codec_chip}Data，g_{codec_chip}DaiDeviceOps和g_{codec_chip}DaiData。
```
struct CodecData g_{codec_chip}Data = {
    .Init = {codec_chip}DeviceInit,
    .Read = {codec_chip}CodecReadReg,
    .Write = {codec_chip}CodecWriteReg,
};

struct AudioDaiOps g_{codec_chip}DaiDeviceOps = {
    .Startup = {codec_chip}DaiStartup,
    .HwParams = {codec_chip}DaiHwParams,
    .Trigger = {codec_chip}NormalTrigger,
};

struct DaiData g_{codec_chip}DaiData = {
    .DaiInit = {codec_chip}DaiDeviceInit,
    .Read = {codec_chip}CodecDaiReadReg,
    .Write = {codec_chip}CodecDaiWriteReg,
    .ops = &g_{codec_chip}DaiDeviceOps,//指向上面定义的操作数,非函数指针
};
```
这三个结构体涉及到的函数均需要在impl.c中进行具体实现，也就是由开发者完成，总结如下：
```
int32_t {codec_chip}DeviceInit(struct AudioCard *audioCard, const struct CodecDevice *device);
int32_t {codec_chip}DeviceRegRead(uint32_t reg, uint32_t *val);
int32_t {codec_chip}DeviceRegWrite(uint32_t reg, uint32_t value);
int32_t {codec_chip}CodecReadReg(const struct CodecDevice *codec, uint32_t reg, uint32_t *val);
int32_t {codec_chip}CodecWriteReg(const struct CodecDevice *codec, uint32_t reg, uint32_t value);
int32_t {codec_chip}RegBitsUpdate(struct AudioMixerControl regAttr);
int32_t {codec_chip}DaiDeviceInit(struct AudioCard *card, const struct DaiDevice *device);
int32_t {codec_chip}DaiStartup(const struct AudioCard *card, const struct DaiDevice *device);
int32_t {codec_chip}DaiHwParams(const struct AudioCard *card, const struct AudioPcmHwParams *param);
int32_t {codec_chip}NormalTrigger(const struct AudioCard *card, int cmd, const struct DaiDevice *device);
int32_t {codec_chip}CodecDaiReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *value);
int32_t {codec_chip}CodecDaiWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);
```
对于linux驱动的注册在{codec_chip}_codec_linux.c中实现，主要是完成了platform驱动的注册。
```
static struct platform_driver {compatible_driver}_codec_driver = {
    .driver = {
        .name = "{compatible_driver}-codec",
        .of_match_table = {compatible_driver}_codec_dt_ids,
    },
    .probe = {compatible_driver}_platform_probe,//开发者实现
    .remove = {compatible_driver}_platform_remove,//自主实现
};
```
```
//开发者根据具体芯片举行设置参数
static const struct regmap_config {compatible_driver}_codec_regmap_config = {
    .name = "{compatible_driver}-codec",
    .reg_bits = 8,
    .val_bits = 8,
    .reg_stride = 1,
    .max_register = 0x4f,
    .cache_type = REGCACHE_FLAT,
    .volatile_reg = {compatible_driver}_volatile_register,//开发者实现的函数
    .writeable_reg = {compatible_driver}_codec_register,//开发者实现的函数
    .readable_reg = {compatible_driver}_codec_register,//开发者实现的函数
    .reg_defaults = {compatible_driver}_reg_defaults,//开发者实现的函数
    .num_reg_defaults = ARRAY_SIZE({compatible_driver}_reg_defaults),
};

struct regmap_config getCodecRegmap(void)
{
    return {compatible_driver}_codec_regmap_config;
}
```

注意{codec_chip}_codec_adapter.c和{codec_chip}_codec_linux_driver.c中头文件引用中还需要包含linux驱动特定芯片的头文件
例如rk809芯片需要包含#include <linux/mfd/rk808.h>
#### dai部分
dai部分与codec部分类似，主要是完成dai的注册和具体实现。
开发者需要在{soc}_dai_ops.c中完成dai的具体实现函数，这些具体实现函数在{soc}_dai_adapter.c中被注册为回调函数。
```
int32_t {soc}DeviceReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *val);
int32_t {soc}DeviceWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);

int32_t {soc}NormalTrigger(const struct AudioCard *card,
    int cmd, const struct DaiDevice *dai);
int32_t {soc}DaiHwParams(const struct AudioCard *card,
    const struct AudioPcmHwParams *param);
int32_t {soc}DaiStartup(const struct AudioCard *card,
    const struct DaiDevice *dai);
int32_t {soc}DaiDeviceInit(struct AudioCard *card,
    const struct DaiDevice *dai);
```
开发者还需要在{soc}_dai_linux_driver.c中注册dai设备的linux驱动节点和
```
static struct platform_driver {soc_company}_i2s_tdm_driver = {
    .probe = {soc_company}_i2s_tdm_probe,//开发者需要完成
    .remove = {soc_company}_i2s_tdm_remove,//开发者需要完成
    .driver = {
        .name = DRV_NAME,
        .of_match_table = of_match_ptr({soc_company}_i2s_tdm_match),
        .pm = NULL,
    },
};
```

```
static int {soc_company}_i2s_tdm_probe(struct platform_device *pdev)
{
   1.内存分配 
   2.读取设备树配置 
   3.获取硬件资源 
   4.驱动数据绑定 
   5.硬件初始配置 
}
```
#### dma部分
soc(dma)部分主要是完成soc相关的dma操作实现，主要是完成dma的注册和具体实现。
{soc}_dma_ops.c：
```
int32_t AudioDmaDeviceInit(const struct AudioCard *card, const struct PlatformDevice *platform);
int32_t {soc}DmaBufAlloc(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaBufFree(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaRequestChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaConfigChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}PcmPointer(struct PlatformData *data, const enum AudioStreamType streamType, uint32_t *pointer);
int32_t {soc}DmaPrep(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaSubmit(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaPending(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaPause(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t {soc}DmaResume(const struct PlatformData *data, const enum AudioStreamType streamType);
```
