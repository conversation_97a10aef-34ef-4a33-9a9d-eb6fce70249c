## Audio Module

The following document uses the `rk809_codec` as an example for explanation. Specific codec chip names have been replaced in the code for illustrative purposes.
The definitions of `<codec_chip>` and `<compatible_driver>` in the YAML are for compatibility with the rk3568 design, if the selected codec chip does not exist.

The audio driver of the ADM framework is primarily divided into four parts: codec, dai, soc (dma), and dsp. Modifications are required for the codec, dai, and soc (dma) parts.
According to the HDF framework requirements, the registration of the specific driver must first be implemented in an adapter file, while the control of the underlying driver is implemented by the Linux kernel's driver framework. Therefore, a corresponding `linux_driver.c` file is needed for Linux device driver registration. The `impl.c` file is the specific implementation file for the operations registered in the adapter.

  - Files in the `codec` directory are mainly for implementing operations for the specific codec chip.
  - Files in the `dai` directory are mainly for implementing operations for the DAI bus.
  - Files in the `soc` directory are mainly for implementing SoC-related DMA operations.
  - The `dsp` directory currently has no specific implementation, only a framework; the functions have no actual operations inside.

### Developer Tasks:

#### Codec Part

Referring to the rk3568 code implementation, the header files in `codec/include` should be changed according to the specific chip's datasheet. For example, `rk817_codec.h` defines register addresses, register bitfields, and other information. `rk809_codec_impl.h` defines structures that may be used and provides external declarations for functions in `rk809_codec_impl.c`.

In `rk3568/audio_drivers/codec/rk809_codec/src/rk809_codec_adapter.c`, private data structures and operations (registering callback functions) are defined, which completes the registration of the HDF driver node. Three private structures are defined: `g_rk809Data`, `g_rk809DaiDeviceOps`, and `g_rk809DaiData`.

```c
struct CodecData g_rk809Data = {
    .Init = Rk809DeviceInit,
    .Read = RK809CodecReadReg,
    .Write = Rk809CodecWriteReg,
};

struct AudioDaiOps g_rk809DaiDeviceOps = {
    .Startup = Rk809DaiStartup,
    .HwParams = Rk809DaiHwParams,
    .Trigger = Rk809NormalTrigger,
};

struct DaiData g_rk809DaiData = {
    .DaiInit = Rk809DaiDeviceInit,
    .Read = RK809CodecDaiReadReg,
    .Write = RK809CodecDaiWriteReg,
    .ops = &g_rk809DaiDeviceOps, // Points to the operations defined above, not a function pointer
};
```

The functions associated with these three structures must be implemented in `impl.c` by the developer. They are summarized as follows:

```c
int32_t Rk809DeviceInit(struct AudioCard *audioCard, const struct CodecDevice *device);
int32_t Rk809DeviceRegRead(uint32_t reg, uint32_t *val);
int32_t Rk809DeviceRegWrite(uint32_t reg, uint32_t value);
int32_t RK809CodecReadReg(const struct CodecDevice *codec, uint32_t reg, uint32_t *val);
int32_t Rk809CodecWriteReg(const struct CodecDevice *codec, uint32_t reg, uint32_t value);
int32_t Rk809RegBitsUpdate(struct AudioMixerControl regAttr);
int32_t Rk809DaiDeviceInit(struct AudioCard *card, const struct DaiDevice *device);
int32_t Rk809DaiStartup(const struct AudioCard *card, const struct DaiDevice *device);
int32_t Rk809DaiHwParams(const struct AudioCard *card, const struct AudioPcmHwParams *param);
int32_t Rk809NormalTrigger(const struct AudioCard *card, int cmd, const struct DaiDevice *device);
int32_t RK809CodecDaiReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *value);
int32_t RK809CodecDaiWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);
```

The Linux driver registration is implemented in `rk809_codec_linux.c`, which primarily completes the registration of the platform driver.

```c
static struct platform_driver rk817_codec_driver = {
    .driver = {
        .name = "rk817-codec",
        .of_match_table = rk817_codec_dt_ids,
    },
    .probe = rk817_platform_probe, // To be implemented by the developer
    .remove = rk817_platform_remove, // To be implemented by the developer
};
```

```c
// The developer sets parameters according to the specific chip
static const struct regmap_config rk817_codec_regmap_config = {
    .name = "rk817-codec",
    .reg_bits = 8,
    .val_bits = 8,
    .reg_stride = 1,
    .max_register = 0x4f,
    .cache_type = REGCACHE_FLAT,
    .volatile_reg = rk817_volatile_register, // Function implemented by the developer
    .writeable_reg = rk817_codec_register, // Function implemented by the developer
    .readable_reg = rk817_codec_register, // Function implemented by the developer
    .reg_defaults = rk817_reg_defaults, // Function implemented by the developer
    .num_reg_defaults = ARRAY_SIZE(rk817_reg_defaults),
};

struct regmap_config getCodecRegmap(void)
{
    return rk817_codec_regmap_config;
}
```

Note that the header file includes in `rk809_codec_adapter.c` and `rk809_codec_linux_driver.c` also need to include the Linux driver header for the specific chip. For example, the rk809 chip requires `#include <linux/mfd/rk808.h>`.

#### DAI Part

The DAI part is similar to the codec part, mainly focused on completing the DAI registration and its specific implementation.
The developer needs to complete the DAI implementation functions in `rk3568_dai_ops.c`. These functions are registered as callback functions in `rk3568_dai_adapter.c`.

```c
int32_t Rk3568DeviceReadReg(const struct DaiDevice *dai, uint32_t reg, uint32_t *val);
int32_t Rk3568DeviceWriteReg(const struct DaiDevice *dai, uint32_t reg, uint32_t value);

int32_t Rk3568NormalTrigger(const struct AudioCard *card,
    int cmd, const struct DaiDevice *dai);
int32_t Rk3568DaiHwParams(const struct AudioCard *card,
    const struct AudioPcmHwParams *param);
int32_t Rk3568DaiStartup(const struct AudioCard *card,
    const struct DaiDevice *dai);
int32_t Rk3568DaiDeviceInit(struct AudioCard *card,
    const struct DaiDevice *dai);
```

The developer also needs to register the DAI device's Linux driver node in `rk3568_dai_linux_driver.c`.

```c
static struct platform_driver rockchip_i2s_tdm_driver = {
    .probe = rockchip_i2s_tdm_probe, // To be completed by the developer
    .remove = rockchip_i2s_tdm_remove, // To be completed by the developer
    .driver = {
        .name = DRV_NAME,
        .of_match_table = of_match_ptr(rockchip_i2s_tdm_match),
        .pm = NULL,
    },
};
```

```c
static int rockchip_i2s_tdm_probe(struct platform_device *pdev)
{
    // 1. Memory allocation
    // 2. Read device tree configuration
    // 3. Get hardware resources
    // 4. Bind driver data
    // 5. Initial hardware configuration
}
```

#### DMA Part

The soc (dma) part is mainly for implementing SoC-related DMA operations, focusing on DMA registration and its specific implementation.
`rk3568_dma_ops.c`:

```c
int32_t AudioDmaDeviceInit(const struct AudioCard *card, const struct PlatformDevice *platform);
int32_t Rk3568DmaBufAlloc(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaBufFree(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaRequestChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaConfigChannel(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568PcmPointer(struct PlatformData *data, const enum AudioStreamType streamType, uint32_t *pointer);
int32_t Rk3568DmaPrep(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaSubmit(const struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaPending(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaPause(struct PlatformData *data, const enum AudioStreamType streamType);
int32_t Rk3568DmaResume(struct PlatformData *data, const enum AudioStreamType streamType);
```