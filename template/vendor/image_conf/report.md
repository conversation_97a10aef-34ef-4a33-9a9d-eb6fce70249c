## image_conf
这个目录定义了最终要烧录到设备上的各个分区镜像（image）里应该包含哪些文件。构建系统会读取这里的配置文件，然后将指定的文件和目录打包成相应的镜像文件，例如 system.img 和 ramdisk.img。
主要修改会集中在：
1. `system_image_conf.txt`: 这是最常需要修改的文件。
  * 添加/删除驱动和HAL库：你的新板卡可能会有不同的硬件，因此需要不同的驱动文件 (.ko) 和硬件抽象层库 (.so)。你需要在这里添加新板卡所需的驱动，并移除不再需要的旧驱动。
  * 添加/删除厂商特定文件：你可能需要为新板卡添加特定的固件（例如 WiFi、蓝牙芯片的固件）、配置文件或预置的二进制程序。
  * 调整预置应用：根据新板卡的硬件能力（例如屏幕尺寸、内存大小），你可能会想要预置不同的应用或移除某些应用。

2. `ramdisk_image_conf.txt`:
  * 修改启动初期所需文件：如果你的新板卡在启动的最初阶段（挂载 system 分区之前）需要特殊的驱动（例如闪存、显示或电源管理的驱动），或者需要不同的 init
    配置文件，你就需要修改这个文件，把相应的文件添加进来。
