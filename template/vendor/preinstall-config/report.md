## preinstall-config 
这个目录用来配置产品的预安装应用列表。系统在首次启动时，会根据这里的配置文件自动安装指定的应用程序。
  * install_list.json: 定义了需要预安装的应用清单。
  * uninstall_list.json: 定义了哪些预安装应用是允许用户卸载的。
  * install_list_permissions.json & install_list_capability.json: 为预安装的应用配置默认的权限和能力。

* 更换板卡修改点:
  * 主要修改 install_list.json。根据新板卡的硬件特性和产品定位，你可能需要增删应用。例如，如果新板卡没有屏幕，就不需要预装图形界面的设置应用；如果新板卡增加了特定的传感器，可能需要预装一个配套的演示或管理应用。
  * 如果应用列表有变化，install_list_permissions.json 可能也需要相应调整。