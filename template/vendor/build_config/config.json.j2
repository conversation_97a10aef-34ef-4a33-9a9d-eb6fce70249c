{
  "product_name": "{{ product_name }}",
  "device_company": "{{ soc_company }}",
  "device_build_path": "device/board/{{ device_company }}/{{ product_name }}",
  "target_cpu": "arm",
  "type": "standard",
  "version": "3.0",
  "board": "{{ product_name }}",
  "api_version": 8,
  "enable_ramdisk": true,
  "enable_absystem": false,
  "build_selinux": true,
  "build_seccomp": true,
  "inherit": [ "productdefine/common/inherit/rich.json", "productdefine/common/inherit/chipset_common.json" ],
  "subsystems": [
    {
      "subsystem": "security",
      "components": [
        {
          "component": "selinux_adapter",
          "features": []
        }
      ]
    },
    {
      "subsystem": "communication",
      "components": [
        {
          "component": "netmanager_ext",
          "features": []
        },
        {
          "component": "bluetooth_service",
          "features": []
        }
      ]
    },
    {
      "subsystem": "hdf",
      "components": [
        {
          "component": "drivers_interface_ril",
          "features": []
        },
        {
          "component": "drivers_peripheral_ril",
          "features":[]
        }
      ]
    },
    {
      "subsystem": "rockchip_products",
      "components": [
        {
          "component": "rockchip_products",
          "features": []
        }
      ]
    },
    {
      "subsystem": "arkui",
      "components": [
        {
          "component": "ace_engine",
          "features": [
            "ace_engine_feature_enable_accessibility = true",
            "ace_engine_feature_enable_web = true"
          ]
        },
        {
          "component": "ui_appearance",
          "features": []
        }
      ]
    },
    {
      "subsystem": "hdf",
      "components": [
        {
          "component": "drivers_interface_audio",
          "features": []
        },
        {
          "component": "drivers_peripheral_audio",
          "features": [
            "drivers_peripheral_audio_feature_full_test_suite = true",
            "drivers_peripheral_audio_feature_alsa_lib = false",
            "drivers_peripheral_audio_feature_effect = true"
          ]
        },
        {
          "component": "drivers_peripheral_codec",
          "features": [
            "drivers_peripheral_codec_feature_support_omx_extend_test = true",
            "drivers_peripheral_codec_feature_support_hdi_v1 = true"
          ]
        },
        {
          "component": "drivers_peripheral_display",
          "features": [
            "drivers_peripheral_display_community = true",
            "drivers_peripheral_display_vdi_default = true"
          ]
        },
        {
          "component": "drivers_peripheral_wlan",
          "features": [
            "drivers_peripheral_wlan_feature_enable_HDF_NL80211 = true",
            "drivers_peripheral_wlan_feature_enable_HDF_UT = false"
          ]
        },
        {
          "component": "hdf_core",
          "features": [
            "hdf_core_platform_test_support = true"
          ]
        }
      ]
    },
    {
      "subsystem": "startup",
      "components": [
        {
          "component": "init",
          "features": [
            "init_feature_ab_partition = true",
            "init_feature_loader = true"
          ]
        }
      ]
    },
    # {
    # "subsystem": "ai",
    # "components": [
    #     {
    #       "component": "mindspore",
    #       "features": []
    #     }
    #   ]
    # },
    {
      "subsystem": "msdp",
      "components": [
        {
          "component": "device_status",
          "features": [
            "device_status_interaction_coordination = false"
          ]
        }
      ]
    },
    {
      "subsystem": "communication",
      "components": [
        {
          "component": "wifi",
          "features": [
            "wifi_feature_non_seperate_p2p = true",
            "wifi_feature_non_hdf_driver = true",
            "wifi_feature_p2p_random_mac_addr = false",
            "wifi_feature_with_hdi_chip_supported = false"
         ]
        }
      ]
    },
    {
      "subsystem": "multimedia",
      "components": [
        {
          "component": "audio_framework",
          "features": [
            "audio_framework_feature_dtmf_tone = true",
            "audio_framework_feature_opensl_es = true",
            "audio_framework_feature_support_os_account = false"
          ]
        }
      ]
    },
    {
      "subsystem": "castplus",
      "components": [
        {
          "component": "sharing_framework",
          "features": []
        },
        {
          "component": "cast_engine",
          "features": []
        }
      ]
    },
    {
      "subsystem": "thirdparty",
      "components": [
        {
          "component": "libuv",
          "features": [
            "libuv_use_ffrt = true"
          ]
        },
        {
          "component": "wpa_supplicant",
          "features": [
            "wpa_supplicant_driver_nl80211 = true"
          ]
        }
      ]
    },
    {
      "subsystem": "tee",
      "components": [
        {
          "component": "tee_client",
          "features": []
        }
      ]
    },
    {
      "subsystem": "usb",
      "components": [
        {
          "component": "usb_manager",
          "features": [
            "usb_manager_feature_pop_up_func_switch_model = false"
          ]
        }
      ]
    },
    {
      "subsystem": "graphic",
      "components": [
        {
          "component": "graphic_2d",
          "features": [
            "graphic_2d_feature_rs_enable_eglimage = true",
            "graphic_2d_feature_use_texgine = true"
          ]
        },
        {
          "component": "graphic_surface",
          "features": []
        },
        {
          "component": "graphic_3d",
          "features": []
        },
        {
          "component": "vulkan-loader",
          "features": []
        },
        {
          "component": "vulkan-headers",
          "features": []
        }
      ]
    },
    {
      "subsystem":"hiviewdfx",
      "components": [
        {
          "component": "hiview",
          "features": [
            "hiview_enable_leak_detector = true",
            "hiview_enable_performance_monitor = true"
          ]
        }
      ]
    }
  ]
}
