 root {
    device_info {
        match_attr = "hdf_manager";
        template host {
            hostName = "";
            priority = 100;
            template device {
                template deviceNode {
                    policy = 0;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "";
                    serviceName = "";
                    deviceMatchAttr = "";
                }
            }
        }
        base :: host {
            hostName = "base_host";
            priority = 50;
            device_support :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 10;
                    permission = 0644;
                    moduleName = "HDF_KEVENT";
                    serviceName = "hdf_kevent";
                }
            }
        }
        platform :: host {
            hostName = "platform_host";
            priority = 50;
            device_gpio :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 10;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_GPIO_MANAGER";
                    serviceName = "HDF_PLATFORM_GPIO_MANAGER";
                }
                device1 :: deviceNode {
                    policy = 0;
                    priority = 10;
                    permission = 0644;
                    moduleName = "linux_gpio_adapter";
                    deviceMatchAttr = "linux_gpio_adapter";
                }
            }
            device_watchdog :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 20;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_WATCHDOG";
                    serviceName = "HDF_PLATFORM_WATCHDOG_0";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_watchdog_0";
                }
            }
           device_rtc :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 30;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_RTC";
                    serviceName = "HDF_PLATFORM_RTC";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_rtc";
                }
            }
            device_uart :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 40;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_UART";
                    serviceName = "HDF_PLATFORM_UART_0";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_uart_0";
                }
                device1 :: deviceNode {
                    policy = 2;
                    permission = 0644;
                    priority = 40;
                    moduleName = "HDF_PLATFORM_UART";
                    serviceName = "HDF_PLATFORM_UART_1";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_uart_1";
                }
                device2 :: deviceNode {
                    policy = 2;
                    permission = 0644;
                    priority = 40;
                    moduleName = "HDF_PLATFORM_UART";
                    serviceName = "HDF_PLATFORM_UART_3";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_uart_3";
                }
            }
            device_i2c :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 50;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_I2C_MANAGER";
                    serviceName = "HDF_PLATFORM_I2C_MANAGER";
                    deviceMatchAttr = "hdf_platform_i2c_manager";
                }
                device1 :: deviceNode {
                    policy = 0;
                    priority = 55;
                    permission = 0644;
                    moduleName = "linux_i2c_adapter";
                    deviceMatchAttr = "linux_i2c_adapter";
                }
            }
            device_adc :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 60;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_ADC_MANAGER";
                    serviceName = "HDF_PLATFORM_ADC_MANAGER";
                    deviceMatchAttr = "hdf_platform_adc_manager";
                }
                device1 :: deviceNode {
                    policy = 0;
                    priority = 65;
                    permission = 0644;
                    moduleName = "linux_adc_adapter";
                    deviceMatchAttr = "linux_adc_adapter_0";
                }
            }
            device_spi :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 60;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_SPI";
                    serviceName = "HDF_PLATFORM_SPI_0";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_spi_0";
                }
                device1 :: deviceNode {
                    policy = 1;
                    priority = 60;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_SPI";
                    serviceName = "HDF_PLATFORM_SPI_1";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_spi_1";
                }
                device2 :: deviceNode {
                    policy = 1;
                    priority = 60;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_SPI";
                    serviceName = "HDF_PLATFORM_SPI_2";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_spi_2";
                }
                device3 :: deviceNode {
                    policy = 1;
                    priority = 60;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_SPI";
                    serviceName = "HDF_PLATFORM_SPI_3";
                    deviceMatchAttr = "{{ soc_company }}_{{ soc }}_spi_3";
                }
            }
            device_sdio :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 70;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_SDIO";
                    serviceName = "HDF_PLATFORM_MMC_2";
                    deviceMatchAttr = "hisilicon_hi35xx_sdio_0";
                }
            }
            device_emmc :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 20;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_EMMC";
                    serviceName = "HDF_PLATFORM_MMC_0";
                    deviceMatchAttr = "hisilicon_hi35xx_emmc_0";
                }
            }
            device_pwm :: device {
               device0 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_PWM";
                    serviceName = "HDF_PLATFORM_PWM_0";
                    deviceMatchAttr = "linux_pwm_adapter_0";
               }
               device1 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_PWM";
                    serviceName = "HDF_PLATFORM_PWM_1";
                    deviceMatchAttr = "linux_pwm_adapter_1";
               }
               device2 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_PWM";
                    serviceName = "HDF_PLATFORM_PWM_2";
                    deviceMatchAttr = "linux_pwm_adapter_2";
               }
               device3 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_PWM";
                    serviceName = "HDF_PLATFORM_PWM_3";
                    deviceMatchAttr = "linux_pwm_adapter_3";
               }
               device4 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    permission = 0644;
                    moduleName = "HDF_PLATFORM_PWM";
                    serviceName = "HDF_PLATFORM_PWM_4";
                    deviceMatchAttr = "linux_pwm_adapter_4";
               }
           }
            device_mipi_dsi:: device {
               device0 :: deviceNode {
                   policy = 0;
                   priority = 150;
                   permission = 0644;
                   moduleName = "HDF_MIPI_TX";
                   serviceName = "HDF_MIPI_TX";
               }
           }
        }
        display :: host {
            hostName = "display_host";
            device_hdf_drm_panel :: device {
                device0 :: deviceNode {
                    policy = 0;
                    priority = 197;
                    preload = 0;
                    moduleName = "HDF_DRMPANEL";
                }
            }
            device_hdf_disp :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 196;
                    permission = 0660;
                    moduleName = "HDF_DISP";
                    serviceName = "hdf_disp";
                }
            }
	    device_hi35xx_disp :: device {
                device0 :: deviceNode {
                    policy = 0;
                    priority = 195;
                    moduleName = "HI351XX_DISP";
                }
            }
            device_lcd :: device {
                device0 :: deviceNode {
                    policy = 0;
                    priority = 100;
                    preload = 2;
                    moduleName = "LITE_LCDKIT";
                    deviceMatchAttr = "hdf_lcdkit_driver";
                }
                device1 :: deviceNode {
                    policy = 0;
                    priority = 100;
                    preload = 0;
                    moduleName = "LCD_ICN9700";
                }
                device2 :: deviceNode {
                    policy = 0;
                    priority = 100;
                    preload = 2;
                    moduleName = "LCD_ST7789";
                }
                device3 :: deviceNode {
                    policy = 0;
                    priority = 100;
                    preload = 0;
                    moduleName = "LCD_ILI9881_ST_5P5";
                }
            }
            device_pwm_bl :: device {
                device0 :: deviceNode {
                    policy = 0;
                    priority = 95;
                    preload = 0;
                    moduleName = "PWM_BL";
                    deviceMatchAttr = "pwm_bl_dev";
                }
            }
            device_backlight :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 90;
                    preload = 0;
                    permission = 0660;
                    moduleName = "HDF_BL";
                    serviceName = "hdf_bl";
                }
            }
        }

        input :: host {
            hostName = "input_host";
            priority = 100;
            device_input_manager :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0660;
                    moduleName = "HDF_INPUT_MANAGER";
                    serviceName = "hdf_input_host";
                    deviceMatchAttr = "";
                }
            }
            device_hdf_touch :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 120;
                    preload = 0;
                    permission = 0660;
                    moduleName = "HDF_TOUCH";
                    serviceName = "hdf_input_event1";
                    deviceMatchAttr = "touch_device1";
                }
            }

            device_touch_chip :: device {
                device0 :: deviceNode {
                    policy = 0;
                    priority = 130;
                    preload = 0;
                    permission = 0660;
                    moduleName = "HDF_TOUCH_GT911";
                    serviceName = "hdf_touch_gt911_service";
                    deviceMatchAttr = "zsj_gt911_5p5";
                }
            }
            device_hdf_hid :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 200;
                    preload = 0;
                    permission = 0660;
                    moduleName = "HDF_HID";
                }
            }
            device_hdf_infrared :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 200;
                    preload = 2;
                    permission = 0660;
                    moduleName = "HDF_INFRARED";
                    serviceName = "hdf_input_event2";
                    deviceMatchAttr = "Infrared_device0";
                }
            }
        }

        network :: host {
            hostName = "network_host";
            device_wifi :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_WIFI";
                    deviceMatchAttr = "hdf_wlan_driver";
                    serviceName = "hdfwifi";
                }
            }
            device_wlan_chips :: device {
                device0 :: deviceNode {
                    policy = 0;
                    preload = 2;
                    moduleName = "HDF_WLAN_CHIPS_AP6275S";
                    deviceMatchAttr = "hdf_wlan_chips_ap6275s";
                    serviceName = "ap6275s";
                }
            }
        }
        camera :: host {
            hostName = "camera_host0";
            device_camera :: device {
                camera0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_CAMERA";
                    serviceName = "hdfcamera";
                    deviceMatchAttr = "hdf_camera_driver";
                }
            }
            camera_adapter :: device {
                camera0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_CAMERA_ADAPTER";
                    serviceName = "hdfcamera0";
                }
            }
        }
        sensor :: host {
            hostName = "sensor_host";
            device_sensor_manager :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_MGR_AP";
                    serviceName = "hdf_sensor_manager_ap";
                }
            }
            device_sensor_accel :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 110;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_ACCEL";
                    serviceName = "sensor_accel";
                    deviceMatchAttr = "hdf_sensor_accel_driver";
                }
            }
            device_sensor_mxc6655xa :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 120;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_ACCEL_MXC6655XA";
                    serviceName = "hdf_accel_mxc6655xa";
                    deviceMatchAttr = "hdf_sensor_accel_mxc6655xa_driver";
                }
            }
            device_sensor_als :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 110;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_ALS";
                    serviceName = "hdf_sensor_als";
                    deviceMatchAttr = "hdf_sensor_als_driver";
                }
            }
            device_sensor_bh1745 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 120;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_ALS_BH1745";
                    serviceName = "hdf_als_bh1745";
                    deviceMatchAttr = "hdf_sensor_als_bh1745_driver";
                }
            }
            device_sensor_proximity :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 110;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_PROXIMITY";
                    serviceName = "hdf_sensor_proximity";
                    deviceMatchAttr = "hdf_sensor_proximity_driver";
                }
            }
            device_sensor_apds9960 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 120;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_PROXIMITY_APDS9960";
                    serviceName = "hdf_proximity_apds9960";
                    deviceMatchAttr = "hdf_sensor_proximity_adps9960_driver";
                }
            }
            device_sensor_magnetic :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 110;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_MAGNETIC";
                    serviceName = "hdf_sensor_magnetic";
                    deviceMatchAttr = "hdf_sensor_magnetic_driver";
                }
            }
            device_sensor_lsm303 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 120;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_MAGNETIC_LSM303";
                    serviceName = "hdf_magnetic_lsm303";
                    deviceMatchAttr = "hdf_sensor_magnetic_lsm303_driver";
                }
            }
            device_sensor_temperature :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 130;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_TEMPERATURE";
                    serviceName = "hdf_sensor_temperature";
                    deviceMatchAttr = "hdf_sensor_temperature_driver";
                }
            }
            device_temperature_aht20 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 140;
                    preload = 2;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_TEMPERATURE_AHT20";
                    serviceName = "hdf_temperature_aht20";
                    deviceMatchAttr = "hdf_sensor_temperature_aht20_driver";
                }
            }
            device_temperature_sht30 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 150;
                    preload = 2;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_TEMPERATURE_SHT30";
                    serviceName = "hdf_temperature_sht30";
                    deviceMatchAttr = "hdf_sensor_temperature_sht30_driver";
                }
            }
            device_sensor_humidity :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 160;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_HUMIDITY";
                    serviceName = "hdf_sensor_humidity";
                    deviceMatchAttr = "hdf_sensor_humidity_driver";
                }
            }
            device_humidity_aht20 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 170;
                    preload = 2;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_HUMIDITY_AHT20";
                    serviceName = "hdf_humidity_aht20";
                    deviceMatchAttr = "hdf_sensor_humidity_aht20_driver";
                }
            }
            device_humidity_sht30 :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 180;
                    preload = 2;
                    permission = 0664;
                    moduleName = "HDF_SENSOR_HUMIDITY_SHT30";
                    serviceName = "hdf_humidity_sht30";
                    deviceMatchAttr = "hdf_sensor_humidity_sht30_driver";
                }
            }
        }
        usb_pnp_linux :: host {
            hostName = "usb_pnp_linux_host";
            device_usb_pnp_linux :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_USB_PNP_NOTIFY";
                    serviceName = "hdf_usb_pnp_notify_service";
                    deviceMatchAttr = "hdf_usb_pnp_notify_config";
                }
            }
        }
        usb_net_linux :: host {
            hostName = "usb_net_linux_host";
            device_usb_net_linux :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 2;
                    permission = 0664;
                    moduleName = "HDF_USB_NET";
                    serviceName = "hdf_usb_net_service";
                    deviceMatchAttr = "hdf_usb_net_config";
                }
            }
        }
        audio :: host {
            hostName = "audio_host";
            priority = 110;
            device_dai :: device {
                device_primary :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "DAI_{{ soc.upper() }}";
                    serviceName = "dai_service";
                    deviceMatchAttr = "hdf_dai_driver";
                }
                device_hdmi :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "DAI_{{ soc.upper() }}";
                    serviceName = "hdmi_dai_service";
                    deviceMatchAttr = "hdf_hdmi_dai_driver";
                }
            }
            device_codec :: device {
                device_primary :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "CODEC_{{ codec_chip.upper() }}";
                    serviceName = "codec_service_0";
                    deviceMatchAttr = "hdf_codec_driver_0";
                }
                device_hdmi :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "AUDIO_HDMI_CODEC";
                    serviceName = "codec_service_1";
                    deviceMatchAttr = "hdf_codec_driver_1";
                }
                device_usb :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 2;
                    permission = 0664;
                    moduleName = "AUDIO_USB_CODEC";
                    serviceName = "audio_usb_service_0";
                }
            }
            device_dsp :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "DSP_{{ soc.upper() }}";
                    serviceName = "dsp_service_0";
                    deviceMatchAttr = "hdf_dsp_driver";
                }
            }
            device_dma :: device {
                device_primary :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "DMA_{{ soc.upper() }}";
                    serviceName = "dma_service_0";
                    deviceMatchAttr = "hdf_dma_driver";
                }
                device_hdmi :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 0;
                    permission = 0666;
                    moduleName = "DMA_{{ soc.upper() }}";
                    serviceName = "hdmi_dma_service_0";
                    deviceMatchAttr = "hdf_hdmi_dma_driver";
                }
               device_usb :: deviceNode {
                    policy = 1;
                    priority = 50;
                    preload = 2;
                    permission = 0666;
                    moduleName = "AUDIO_USB_DMA";
                    serviceName = "usb_dma_service_0";
                }
            }

            device_audio :: device {
                device_primary :: deviceNode {
                    policy = 2;
                    priority = 60;
                    preload = 0;
                    permission = 0666;
                    moduleName = "HDF_AUDIO";
                    deviceMatchAttr = "hdf_audio_driver_0";
                    serviceName = "hdf_audio_codec_primary_dev0";
                }
                device_hdmi :: deviceNode {
                    policy = 2;
                    priority = 60;
                    preload = 2;
                    permission = 0666;
                    moduleName = "HDF_AUDIO";
                    deviceMatchAttr = "hdf_audio_driver_1";
                    serviceName = "hdf_audio_codec_hdmi_dev0";
                }
                device_usb :: deviceNode {
                    policy = 2;
                    priority = 60;
                    preload = 2;
                    permission = 0666;
                    moduleName = "HDF_AUDIO";
                    deviceMatchAttr = "hdf_audio_driver_2";
                    serviceName = "hdf_audio_codec_usb_dev0";
                }
            }
            device_stream :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    preload = 0;
                    permission = 0666;
                    moduleName = "HDF_AUDIO_STREAM";
                    serviceName = "hdf_audio_render";
                }
                device1 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    preload = 0;
                    permission = 0666;
                    moduleName = "HDF_AUDIO_STREAM";
                    serviceName = "hdf_audio_capture";
                }
            }
            device_control :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 80;
                    preload = 0;
                    permission = 0666;
                    moduleName = "HDF_AUDIO_CONTROL";
                    serviceName = "hdf_audio_control";
                }
            }
            device_analog_headset :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 90;
                    preload = 0;
                    permission = 0666;
                    moduleName = "AUDIO_ANALOG_HEADSET";
                    serviceName = "analog_headset_service";
                    deviceMatchAttr = "analog_headset_attr";
                }
            }
        }
        vibrator :: host {
            hostName = "vibrator_host";
            device_vibrator :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_VIBRATOR";
                    serviceName = "hdf_misc_vibrator";
                    deviceMatchAttr = "hdf_vibrator_driver";
                }
            }
            device_linear_vibrator :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 105;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_LINEAR_VIBRATOR";
                    serviceName = "hdf_misc_linear_vibrator";
                    deviceMatchAttr = "hdf_linear_vibrator_driver";
                }
            }
            device_drv2605l_vibrator :: device {
                device0 :: deviceNode {
                    policy = 1;
                    priority = 105;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_DRV2605L_VIBRATOR";
                    serviceName = "hdf_misc_drv2605l_vibrator";
                    deviceMatchAttr = "hdf_drv2605l_linear_vibrator_driver";
                }
            }
        }
        light :: host {
            hostName = "light_host";
            device_light :: device {
                device0 :: deviceNode {
                    policy = 2;
                    priority = 100;
                    preload = 0;
                    permission = 0664;
                    moduleName = "HDF_LIGHT";
                    serviceName = "hdf_light";
                    deviceMatchAttr = "hdf_light_driver";
                }
            }
        }
    }
}
