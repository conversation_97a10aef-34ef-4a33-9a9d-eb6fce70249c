root {
    platform {
        adc_config {
            match_attr = "linux_adc_adapter_0";
            template adc_device {
                serviceName = "";
                channelNum = 2;
                driver_channel0_name = "";
                driver_channel1_name = "";
                deviceNum = 0;
                scanMode = 0;
                rate = 100;
            }
            device_adc_0x0000 :: adc_device {
                channelNum = 8;
                driver_channel0_name = "/sys/bus/iio/devices/iio:device0/in_voltage0_raw";
                driver_channel1_name = "/sys/bus/iio/devices/iio:device0/in_voltage1_raw";
                driver_channel2_name = "/sys/bus/iio/devices/iio:device0/in_voltage2_raw";
                driver_channel3_name = "/sys/bus/iio/devices/iio:device0/in_voltage3_raw";
                driver_channel4_name = "/sys/bus/iio/devices/iio:device0/in_voltage4_raw";
                driver_channel5_name = "/sys/bus/iio/devices/iio:device0/in_voltage5_raw";
                driver_channel6_name = "/sys/bus/iio/devices/iio:device0/in_voltage6_raw";
                driver_channel7_name = "/sys/bus/iio/devices/iio:device0/in_voltage7_raw";
                deviceNum = 0;
            }
            device_adc_0x0001 :: adc_device {
                channelNum = 2;
                driver_channel0_name = "/sys/bus/iio/devices/iio:device0/in_voltage0_raw";
                driver_channel1_name = "/sys/bus/iio/devices/iio:device0/in_voltage1_raw";
                deviceNum = 14;
            }
        }
    }
}
