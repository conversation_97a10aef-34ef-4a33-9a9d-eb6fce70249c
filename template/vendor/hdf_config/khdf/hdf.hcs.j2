#include "device_info/device_info.hcs"
#include "platform/adc_config_linux.hcs"
#include "platform/pwm_config.hcs"
#include "platform/{{ soc }}_watchdog_config.hcs"
#include "platform/{{ soc }}_uart_config.hcs"
#include "platform/sdio_config.hcs"
#include "platform/emmc_config.hcs"
#include "platform/{{ soc }}_spi_config.hcs"
#include "input/input_config.hcs"
#include "wifi/wlan_platform.hcs"
#include "wifi/wlan_chip_ap6275s.hcs"
#include "camera/camera_config.hcs"
#include "sensor/sensor_config.hcs"
#include "audio/audio_config.hcs"
#include "audio/codec_config.hcs"
#include "audio/dai_config.hcs"
#include "audio/dma_config.hcs"
#include "audio/dsp_config.hcs"
#include "audio/analog_headset_config.hcs"
#include "light/light_config.hcs"
#include "vibrator/vibrator_config.hcs"
#include "vibrator/linear_vibrator_config.hcs"
#include "vibrator/drv2605l_linear_vibrator_config.hcs"
#include "lcd/lcd_config.hcs"

root {
    module = "{{ soc_company }},{{ soc }}_chip";
}
