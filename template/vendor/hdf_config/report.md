## hdf_config
OHOS系统中RK3568平台的HDF(Hardware Driver Framework)硬件驱动框架配置目录，包含了所有硬件设备的配置文件，分为内核态设备配置文件(khdf)和用户态设备配置文件(uhdf)
修改 HDF 配置来适配新的硬件。这通常是一个细致的工作，主要涉及以下几个方面：
1.  `device_info.hcs`：这是核心文件。你需要根据新板卡的硬件，修改或替换里面的 deviceNode。
   * 添加/删除/修改设备节点。
   * 修改 moduleName 来指定新的或不同的驱动模块。
   * 修改 deviceMatchAttr，这是驱动与设备匹配的关键属性，必须与新硬件的驱动实现相对应。
2. 平台和外设配置文件 (例如 platform/、audio/、sensor/ 等目录下的 .hcs 文件):
  * 引脚配置 (GPIO)：修改与引脚相关的配置，例如 input/input_config.hcs 中的按键、触摸屏引脚，audio/analog_headset_config.hcs 中的耳机检测引脚等。
  * 总线配置 (I2C, SPI, UART)：修改 platform/ 目录下的 i2c_config.hcs、rk3568_spi_config.hcs、rk3568_uart_config.hcs 等文件，更新总线号、设备地址等。
  * 传感器配置 (`sensor/`)：如果传感器型号或连接方式不同，需要修改对应传感器配置文件中的 I2C 地址、中断引脚、寄存器初始化序列等。
  * 音频配置 (`audio/`)：修改 Codec、DAI、DMA 的寄存器配置、连接关系等。
    * dai_config中的idInfo,涉及具体的设备树地址和寄存器配置
  * WiFi/蓝牙配置 (`wifi/`)：修改 wlan_platform.hcs 和具体芯片的配置文件，以适应新的无线模块。
3. 修改用户态驱动配置 (`uhdf` 目录)：
  * `device_info.hcs`：与内核态类似，这里定义了用户态服务（Host）和驱动的加载信息。例如蓝牙、USB、相机、音频等用户态服务都在这里配置。你需要根据新板卡的功能调整这些配置。
  * 媒体和相机配置：如果新板卡的摄像头或媒体处理能力不同，需要修改 camera/ 和 media_codec/ 下的配置文件，例如支持的格式、分辨率、处理流程 (pipeline) 等。
4. 修改编译脚本：
  * khdf/Makefile 和 uhdf/BUILD.gn 可能需要调整，特别是 uhdf/BUILD.gn 中的 part_name 和 subsystem_name 需要更新为你新产品的名称。