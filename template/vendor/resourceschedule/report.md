## resourceschedule
这个目录负责系统的资源调度和性能配置，目的是优化系统性能和功耗，确保流畅的用户体验。
  * cgroup_sched/: Cgroup (控制组) 相关的调度配置，用于精细化控制进程组的CPU、内存等资源。
  * ressched/: 资源调度（Resource Schedule）服务的核心配置。
  * soc_perf/: SoC (芯片) 性能管理配置。这里定义了不同场景下的性能策略，例如触摸屏幕时瞬间提升CPU频率以保证跟手度（Boost策略）。
更换板卡修改点:
  * 这个目录的配置与硬件（特别是SoC）强相关，更换板卡（尤其是更换SoC时）必须重点修改。
  * 你需要根据新SoC的规格，重写 soc_perf/socperf_resource_config.xml，在其中定义正确的CPU、GPU、DDR等硬件单元的频率档位和功耗信息。
  * 需要根据新板卡的性能和散热表现，重新调整 soc_perf/socperf_boost_config.xml 中的性能“超频”策略。
  * ressched 和 cgroup 的配置也可能需要微调，以达到新硬件的最佳能效比。