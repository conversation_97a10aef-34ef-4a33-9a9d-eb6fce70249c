# Copyright (c) 2023 Huawei Device Co., Ltd.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import("//build/ohos.gni")
import("//drivers/hdf_core/adapter/uhdf2/uhdf.gni")
import("$hdf_framework_path/tools/hc-gen/hc_gen.gni")

hc_gen("build_codec_adapter_capabilities") {
  sources = [ rebase_path(
          "../../hdf_config/uhdf/media_codec/codec_adapter_capabilities.hcs") ]
}

ohos_prebuilt_etc("codec_adapter_capabilities.hcb") {
  deps = [ ":build_codec_adapter_capabilities" ]
  hcs_outputs = get_target_outputs(":build_codec_adapter_capabilities")
  source = hcs_outputs[0]
  relative_install_dir = "hdfconfig"
  install_images = [ chipset_base_dir ]
  subsystem_name = "product_hihope"
  part_name = "product_{{ product_name }}"
}

group("hdf_codec_config") {
  deps = [ ":codec_adapter_capabilities.hcb" ]
}
