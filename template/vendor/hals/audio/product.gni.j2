# Copyright (C) 2021-2023 HiHope Open Source Organization .
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

enable_audio_alsa_mode = false
drivers_peripheral_audio_feature_hdf_proxy_stub = true
drivers_peripheral_audio_feature_hal_notsupport_pathselect = false
enable_audio_analog_headset = true
drivers_peripheral_audio_feature_policy_config = true
drivers_peripheral_audio_feature_alsa_lib = false
drivers_peripheral_audio_feature_effect = true
