#!/bin/bash

# HDF配置文件复制脚本
# 支持两种模式：
# 1. 复制指定目录下的所有文件
# 2. 复制指定路径下的指定文件

# 配置要复制的子目录（目录模式）
COPY_SUBDIRS=(

)

# 配置要复制的指定文件（文件模式）
# 格式：相对于hdf_config的路径
COPY_FILES=(
    "display_manager_config.xml"
    "window_manager_config.xml"
)

# 复制模式配置
# 可选值：
# - "directory": 复制指定目录下的所有文件
# - "file": 复制指定的文件
# - "both": 同时使用两种模式
COPY_MODE="${COPY_MODE:-both}"

# 检查环境变量
if [ -z "$TEMPLATE_OHOS_PATH" ]; then
    echo "错误: TEMPLATE_OHOS_PATH 环境变量未设置"
    exit 1
fi

if [ -z "$TEMPLATE_PRODUCT_NAME" ]; then
    echo "错误: TEMPLATE_PRODUCT_NAME 环境变量未设置"
    exit 1
fi

# Source directory path (needs to be adjusted according to the actual OpenHarmony directory structure)
# Change the path as needed, using rk3568 as the base template
SOURCE_BASE_DIR="$TEMPLATE_OHOS_PATH/vendor/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/window_config"

# 检查源基础目录是否存在
if [ ! -d "$SOURCE_BASE_DIR" ]; then
    echo "警告: 未找到hdf_config源目录，尝试的路径: $SOURCE_BASE_DIR"
    echo "跳过文件复制"
    exit 0
fi

# 目标目录（输出目录下对应的位置）
# 构建输出目录路径：output/{ohos_version}/{product_name}/device/camera
if [ -z "$TEMPLATE_OHOS_VERSION" ]; then
    TEMPLATE_OHOS_VERSION="5.0.0"
fi

# 项目根目录从命令行参数获取，如果没有提供则尝试自动计算
if [ -n "$1" ]; then
    PROJECT_ROOT="$1"
    echo "使用传入的项目根目录: $PROJECT_ROOT"
else
    # 回退到自动计算（兼容旧的调用方式）
    # 脚本路径: template/device/board/camera/copy_file.sh
    # 需要向上4级到达项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
    echo "自动计算项目根目录: $PROJECT_ROOT"
fi

# 构建目标目录路径 - Vendor 目录结构
TARGET_DIR="$PROJECT_ROOT/output/$TEMPLATE_OHOS_VERSION/$TEMPLATE_PRODUCT_NAME/vendor/$TEMPLATE_DEVICE_COMPANY/$TEMPLATE_PRODUCT_NAME/preinstall-config"

# 确保目标目录存在
mkdir -p "$TARGET_DIR"

echo "目标目录: $TARGET_DIR"
echo "复制模式: $COPY_MODE"
echo "从 $SOURCE_BASE_DIR 复制hdf_config文件到 $TARGET_DIR"

# 复制指定目录的函数
copy_directories() {
    local copied_count=0
    echo "=== 目录复制模式 ==="

    for subdir in "${COPY_SUBDIRS[@]}"; do
        source_subdir="$SOURCE_BASE_DIR/$subdir"
        target_subdir="$TARGET_DIR/$subdir"

        if [ -d "$source_subdir" ]; then
            echo "复制子目录: $subdir"
            # 确保目标子目录存在
            mkdir -p "$target_subdir"
            # 复制子目录内容
            cp -r "$source_subdir"/* "$target_subdir/" 2>/dev/null || {
                echo "警告: 复制子目录 $subdir 时出现错误"
            }
            ((copied_count++))
        else
            echo "警告: 源子目录不存在: $source_subdir"
        fi
    done

    echo "目录复制完成，成功复制 $copied_count 个子目录"
    return $copied_count
}

# 复制指定文件的函数
copy_files() {
    local copied_count=0
    echo "=== 文件复制模式 ==="

    for file_path in "${COPY_FILES[@]}"; do
        source_file="$SOURCE_BASE_DIR/$file_path"
        target_file="$TARGET_DIR/$file_path"
        target_file_dir="$(dirname "$target_file")"

        if [ -f "$source_file" ]; then
            echo "复制文件: $file_path"
            # 确保目标文件目录存在
            mkdir -p "$target_file_dir"
            # 复制文件
            cp "$source_file" "$target_file" || {
                echo "警告: 复制文件 $file_path 时出现错误"
                continue
            }
            ((copied_count++))
        else
            echo "警告: 源文件不存在: $source_file"
        fi
    done

    echo "文件复制完成，成功复制 $copied_count 个文件"
    return $copied_count
}

# 根据模式执行复制
total_copied=0
case "$COPY_MODE" in
    "directory")
        copy_directories
        total_copied=$?
        ;;
    "file")
        copy_files
        total_copied=$?
        ;;
    "both")
        copy_directories
        dir_copied=$?
        copy_files
        file_copied=$?
        total_copied=$((dir_copied + file_copied))
        ;;
    *)
        echo "错误: 不支持的复制模式: $COPY_MODE"
        echo "支持的模式: directory, file, both"
        exit 1
        ;;
esac

# 输出最终结果
if [ $total_copied -gt 0 ]; then
    echo "=== 复制完成 ==="
    echo "hdf_config文件复制完成，总共成功复制 $total_copied 个项目"
    echo "目标目录: $TARGET_DIR"

    # 显示复制后的目录结构
    if command -v tree >/dev/null 2>&1; then
        echo "复制后的目录结构:"
        tree "$TARGET_DIR" -L 3
    else
        echo "复制后的目录内容:"
        find "$TARGET_DIR" -type f | head -20
    fi
else
    echo "警告: 没有找到任何指定的文件或目录"
    echo "创建空的hdf_config目录作为占位符"
    # 创建基本的目录结构
    for subdir in "${COPY_SUBDIRS[@]}"; do
        mkdir -p "$TARGET_DIR/$subdir"
        echo "创建空目录: $TARGET_DIR/$subdir"
    done
fi

echo "=== 脚本执行完成 ==="
echo "目标目录: $TARGET_DIR"
echo "复制模式: $COPY_MODE"

# 提供使用说明
echo ""
echo "使用说明:"
echo "1. 设置环境变量 COPY_MODE 来选择复制模式:"
echo "   - export COPY_MODE=directory  # 只复制目录"
echo "   - export COPY_MODE=file       # 只复制指定文件"
echo "   - export COPY_MODE=both       # 同时复制目录和文件（默认）"
echo "2. 修改脚本中的 COPY_SUBDIRS 数组来指定要复制的目录"
echo "3. 修改脚本中的 COPY_FILES 数组来指定要复制的文件"
