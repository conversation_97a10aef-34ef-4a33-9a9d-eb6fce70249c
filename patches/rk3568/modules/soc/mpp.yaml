# MPP模块patch配置
version: "1.0"
product_name: "rk3568"
module_name: "mpp"
description: "RK3568媒体处理平台(MPP)模块配置"

code_modifications:
  # 函数替换 - MPP初始化
  function_replacements:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/mpp/mpp_manager.cpp"
      function_signature: "int MppManager::Initialize()"
      description: "自定义MPP管理器初始化"
      replacement_code: |
        int MppManager::Initialize()
        {
            MPP_LOGI("Custom MPP Manager initialization for RK3568");
            
            // 初始化MPP硬件
            if (InitializeMppHardware() != 0) {
                MPP_LOGE("Failed to initialize MPP hardware");
                return -1;
            }
            
            // 配置编解码器
            MppCodecConfig codecConfig = {
                .enableH264 = true,
                .enableH265 = true,
                .enableVP8 = true,
                .enableVP9 = true,
                .enableJPEG = true,
                .maxWidth = 4096,
                .maxHeight = 2160,
                .maxFrameRate = 60
            };
            
            if (ConfigureCodecs(codecConfig) != 0) {
                MPP_LOGE("Failed to configure codecs");
                return -1;
            }
            
            // 初始化内存池
            if (InitializeMemoryPool() != 0) {
                MPP_LOGE("Failed to initialize memory pool");
                return -1;
            }
            
            // 启用硬件加速
            EnableHardwareAcceleration();
            
            MPP_LOGI("MPP Manager initialized successfully");
            return 0;
        }

  # 函数添加 - MPP编解码器配置
  function_additions:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/mpp/mpp_manager.cpp"
      position: "end_of_class"
      description: "添加MPP编解码器配置功能"
      code: |
        int MppManager::ConfigureCodecs(const MppCodecConfig& config)
        {
            MPP_LOGI("Configuring MPP codecs");
            
            // 配置H.264编解码器
            if (config.enableH264) {
                if (ConfigureH264Codec(config.maxWidth, config.maxHeight, config.maxFrameRate) != 0) {
                    MPP_LOGE("Failed to configure H.264 codec");
                    return -1;
                }
            }
            
            // 配置H.265编解码器
            if (config.enableH265) {
                if (ConfigureH265Codec(config.maxWidth, config.maxHeight, config.maxFrameRate) != 0) {
                    MPP_LOGE("Failed to configure H.265 codec");
                    return -1;
                }
            }
            
            // 配置JPEG编解码器
            if (config.enableJPEG) {
                if (ConfigureJPEGCodec(config.maxWidth, config.maxHeight) != 0) {
                    MPP_LOGE("Failed to configure JPEG codec");
                    return -1;
                }
            }
            
            return 0;
        }
        
        int MppManager::EnableHardwareAcceleration()
        {
            MPP_LOGI("Enabling MPP hardware acceleration");
            
            // 启用RK3568特定的硬件加速功能
            EnableRK3568VpuAcceleration();
            EnableRK3568RgaAcceleration();
            EnableRK3568IepAcceleration();
            
            return 0;
        }
        
        int MppManager::InitializeMemoryPool()
        {
            MPP_LOGI("Initializing MPP memory pool");
            
            // 为不同类型的媒体数据分配内存池
            AllocateVideoMemoryPool(64 * 1024 * 1024);  // 64MB for video
            AllocateAudioMemoryPool(16 * 1024 * 1024);  // 16MB for audio
            AllocateImageMemoryPool(32 * 1024 * 1024);  // 32MB for images
            
            return 0;
        }

  # 头文件修改 - 添加MPP配置结构
  header_modifications:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/mpp/mpp_manager.h"
      description: "添加MPP配置结构和方法声明"
      additions:
        - position: "before_class"
          code: |
            // MPP编解码器配置结构
            struct MppCodecConfig {
                bool enableH264;
                bool enableH265;
                bool enableVP8;
                bool enableVP9;
                bool enableJPEG;
                uint32_t maxWidth;
                uint32_t maxHeight;
                uint32_t maxFrameRate;
            };
            
        - position: "public_methods"
          code: |
            // MPP配置方法
            int ConfigureCodecs(const MppCodecConfig& config);
            int EnableHardwareAcceleration();
            int InitializeMemoryPool();
            
        - position: "private_methods"
          code: |
            // MPP硬件控制
            int InitializeMppHardware();
            int ConfigureH264Codec(uint32_t width, uint32_t height, uint32_t fps);
            int ConfigureH265Codec(uint32_t width, uint32_t height, uint32_t fps);
            int ConfigureJPEGCodec(uint32_t width, uint32_t height);
            void EnableRK3568VpuAcceleration();
            void EnableRK3568RgaAcceleration();
            void EnableRK3568IepAcceleration();
            void AllocateVideoMemoryPool(size_t size);
            void AllocateAudioMemoryPool(size_t size);
            void AllocateImageMemoryPool(size_t size);

  # 文件patch - MPP构建配置
  file_patches:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/mpp/BUILD.gn"
      description: "MPP模块构建配置"
      backup: false
      patch_content: |
        @@ -20,6 +20,10 @@
           deps = [
             "//foundation/multimedia/media_foundation:media_foundation",
             "//drivers/peripheral/codec/interfaces/include:codec_interface",
        +    "//vendor/rockchip/hardware/mpp:rk_mpp",
        +    "//vendor/rockchip/hardware/rga:rk_rga",
        +    "//vendor/rockchip/hardware/iep:rk_iep",
        +    "//third_party/ffmpeg:ffmpeg_shared",
           ]
        +  defines = [ "ENABLE_RK3568_MPP", "ENABLE_HARDWARE_ACCELERATION" ]
         }

# 复制配置
copy_targets:
  - source: "output/5.0.0/rk3568/soc/rk3568/mpp/"
    destination: "target/mpp/"
    include_patterns:
      - "*.cpp"
      - "*.h"
      - "BUILD.gn"
      - "*.json"
    preserve_structure: true
