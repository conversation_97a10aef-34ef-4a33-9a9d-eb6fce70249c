# GPU模块patch配置
version: "1.0"
product_name: "rk3568"
module_name: "gpu"
description: "RK3568 GPU模块配置"

code_modifications:
  # 函数替换 - GPU驱动初始化
  function_replacements:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/gpu/mali_driver.cpp"
      function_signature: "int MaliDriver::Initialize()"
      description: "自定义Mali GPU驱动初始化"
      replacement_code: |
        int MaliDriver::Initialize()
        {
            GPU_LOGI("Custom Mali GPU driver initialization for RK3568");
            
            // 初始化GPU硬件
            if (InitializeGpuHardware() != 0) {
                GPU_LOGE("Failed to initialize GPU hardware");
                return -1;
            }
            
            // 配置GPU参数
            GpuConfig config = {
                .coreCount = 2,           // RK3568 Mali-G52 2EE
                .maxFrequency = 800,      // 800MHz
                .memorySize = 128,        // 128MB GPU memory
                .enablePowerManagement = true,
                .enableThermalThrottling = true
            };
            
            if (ConfigureGpu(config) != 0) {
                GPU_LOGE("Failed to configure GPU");
                return -1;
            }
            
            // 初始化OpenGL ES支持
            if (InitializeOpenGLES() != 0) {
                GPU_LOGE("Failed to initialize OpenGL ES");
                return -1;
            }
            
            // 初始化Vulkan支持
            if (InitializeVulkan() != 0) {
                GPU_LOGW("Vulkan initialization failed, continuing without Vulkan support");
            }
            
            GPU_LOGI("Mali GPU driver initialized successfully");
            return 0;
        }

  # 函数添加 - GPU性能管理
  function_additions:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/gpu/mali_driver.cpp"
      position: "end_of_class"
      description: "添加GPU性能管理功能"
      code: |
        int MaliDriver::ConfigureGpu(const GpuConfig& config)
        {
            GPU_LOGI("Configuring Mali GPU with %d cores at %dMHz", 
                    config.coreCount, config.maxFrequency);
            
            // 设置GPU频率
            if (SetGpuFrequency(config.maxFrequency) != 0) {
                GPU_LOGE("Failed to set GPU frequency");
                return -1;
            }
            
            // 配置内存
            if (ConfigureGpuMemory(config.memorySize) != 0) {
                GPU_LOGE("Failed to configure GPU memory");
                return -1;
            }
            
            // 启用电源管理
            if (config.enablePowerManagement) {
                EnablePowerManagement();
            }
            
            // 启用温度控制
            if (config.enableThermalThrottling) {
                EnableThermalThrottling();
            }
            
            return 0;
        }
        
        int MaliDriver::InitializeOpenGLES()
        {
            GPU_LOGI("Initializing OpenGL ES support");
            
            // 初始化OpenGL ES 3.2支持
            if (InitializeGLES32() != 0) {
                GPU_LOGE("Failed to initialize OpenGL ES 3.2");
                return -1;
            }
            
            // 启用GPU扩展
            EnableGpuExtensions();
            
            return 0;
        }
        
        int MaliDriver::InitializeVulkan()
        {
            GPU_LOGI("Initializing Vulkan support");
            
            // 检查Vulkan支持
            if (!CheckVulkanSupport()) {
                GPU_LOGW("Vulkan not supported on this hardware");
                return -1;
            }
            
            // 初始化Vulkan 1.1
            if (InitializeVulkan11() != 0) {
                GPU_LOGE("Failed to initialize Vulkan 1.1");
                return -1;
            }
            
            return 0;
        }

  # 头文件修改 - 添加GPU配置结构
  header_modifications:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/gpu/mali_driver.h"
      description: "添加GPU配置结构和方法声明"
      additions:
        - position: "before_class"
          code: |
            // GPU配置结构
            struct GpuConfig {
                uint32_t coreCount;
                uint32_t maxFrequency;        // MHz
                uint32_t memorySize;          // MB
                bool enablePowerManagement;
                bool enableThermalThrottling;
            };
            
        - position: "public_methods"
          code: |
            // GPU配置方法
            int ConfigureGpu(const GpuConfig& config);
            int InitializeOpenGLES();
            int InitializeVulkan();
            
        - position: "private_methods"
          code: |
            // GPU硬件控制
            int InitializeGpuHardware();
            int SetGpuFrequency(uint32_t frequency);
            int ConfigureGpuMemory(uint32_t sizeInMB);
            void EnablePowerManagement();
            void EnableThermalThrottling();
            int InitializeGLES32();
            void EnableGpuExtensions();
            bool CheckVulkanSupport();
            int InitializeVulkan11();

  # 文件patch - GPU构建配置
  file_patches:
    - file_path: "output/5.0.0/rk3568/soc/rk3568/gpu/BUILD.gn"
      description: "GPU模块构建配置"
      backup: false
      patch_content: |
        @@ -15,6 +15,10 @@
           deps = [
             "//foundation/graphic/graphic_2d:librender_service",
             "//drivers/peripheral/display/interfaces/include:display_interface",
        +    "//vendor/arm/mali:mali_driver",
        +    "//third_party/opengl:gles_headers",
        +    "//third_party/vulkan:vulkan_headers",
        +    "//vendor/rockchip/hardware/gpu:rk3568_gpu_config",
           ]
        +  defines = [ "MALI_G52_SUPPORT", "ENABLE_VULKAN_SUPPORT", "ENABLE_GLES32_SUPPORT" ]
         }

# 复制配置
copy_targets:
  - source: "output/5.0.0/rk3568/soc/rk3568/gpu/"
    destination: "target/gpu/"
    include_patterns:
      - "*.cpp"
      - "*.h"
      - "BUILD.gn"
    preserve_structure: true
