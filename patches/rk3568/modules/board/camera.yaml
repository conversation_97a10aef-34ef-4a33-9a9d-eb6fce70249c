# Camera模块patch配置
version: "1.0"
product_name: "rk3568"
module_name: "camera"
description: "RK3568相机驱动模块配置"

code_modifications:
  # 函数替换 - 相机预览节点
  function_replacements:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
      description: "自定义相机预览节点停止函数"
      replacement_code: |
        RetCode MyPreviewNode::Stop(const int32_t streamId)
        {
            CAMERA_LOGI("Custom Camera Stop implementation for stream %d", streamId);
            // 相机特定的停止逻辑
            if (streamId < 0) {
                CAMERA_LOGE("Invalid stream ID: %d", streamId);
                return RC_ERROR;
            }
            
            // 清理相机资源
            CleanupCameraResources();
            
            // 停止相机流
            if (StopCameraStream(streamId) != RC_OK) {
                CAMERA_LOGE("Failed to stop camera stream %d", streamId);
                return RC_ERROR;
            }
            
            CAMERA_LOGI("Camera stream %d stopped successfully", streamId);
            return RC_OK;
        }

  # 函数添加 - 相机缓冲区处理
  function_additions:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
      position: "end_of_class"
      description: "添加相机缓冲区处理函数"
      code: |
        RetCode MyCaptureNode::ProcessCameraBuffer(std::shared_ptr<IBuffer>& buffer)
        {
            CAMERA_LOGI("Processing camera buffer");
            
            // 检查缓冲区有效性
            if (!buffer || buffer->GetSize() == 0) {
                CAMERA_LOGE("Invalid camera buffer");
                return RC_ERROR;
            }
            
            // 应用相机特定的图像处理
            if (ApplyCameraImageProcessing(buffer) != RC_OK) {
                CAMERA_LOGE("Failed to apply camera image processing");
                return RC_ERROR;
            }
            
            return RC_OK;
        }
        
        RetCode MyCaptureNode::ApplyCameraImageProcessing(std::shared_ptr<IBuffer>& buffer)
        {
            // 实现相机图像处理逻辑
            // 例如：白平衡、曝光调整、降噪等
            CAMERA_LOGI("Applying camera image processing");
            return RC_OK;
        }

  # 头文件修改 - 添加相机方法声明
  header_modifications:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.h"
      description: "添加相机处理方法声明"
      additions:
        - position: "public_methods"
          code: |
            // 相机缓冲区处理方法
            RetCode ProcessCameraBuffer(std::shared_ptr<IBuffer>& buffer);
            
        - position: "private_methods"
          code: |
            // 相机图像处理方法
            RetCode ApplyCameraImageProcessing(std::shared_ptr<IBuffer>& buffer);
            RetCode CleanupCameraResources();
            RetCode StopCameraStream(int32_t streamId);

  # 文件patch - 相机构建配置
  file_patches:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/BUILD.gn"
      description: "相机模块构建配置"
      backup: false
      patch_content: |
        @@ -28,6 +28,8 @@
           deps = [
             "//drivers/peripheral/camera/hal/utils:camera_utils",
             "//third_party/libjpeg-turbo:turbojpeg_static",
        +    "//vendor/custom/camera:camera_extensions",
        +    "//drivers/peripheral/camera/custom:rk3568_camera_driver",
           ]
        +  defines = [ "ENABLE_CAMERA_EXTENSIONS", "RK3568_CAMERA_SUPPORT" ]
         }

# 复制配置 - 指定需要复制的文件和目录
copy_targets:
  - source: "output/5.0.0/rk3568/board/camera/"
    destination: "target/camera/"
    include_patterns:
      - "*.cpp"
      - "*.h"
      - "BUILD.gn"
      - "*.json"
    exclude_patterns:
      - "*.tmp"
      - "*.bak"
    preserve_structure: true
