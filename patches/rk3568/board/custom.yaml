# Board层自定义代码配置
version: "1.0"
product_name: "rk3568"

code_modifications:
  # 函数替换 - 适合复杂的函数级修改
  function_replacements:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
      description: "自定义预览节点停止函数"
      replacement_code: |
        RetCode MyPreviewNode::Stop(const int32_t streamId)
        {
            CAMERA_LOGI("Custom Board Stop implementation for stream %d", streamId);
            // Board层特定的停止逻辑
            if (streamId < 0) {
                CAMERA_LOGE("Invalid stream ID: %d", streamId);
                return RC_ERROR;
            }
            
            // 清理Board层资源
            CleanupBoardResources();
            
            return RC_OK;
        }

  # 函数添加 - 适合添加新函数
  function_additions:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
      position: "end_of_class"
      description: "添加Board层自定义处理函数"
      code: |
        RetCode MyCaptureNode::ProcessBoardSpecificBuffer(std::shared_ptr<IBuffer>& buffer)
        {
            CAMERA_LOGI("Processing board-specific buffer");
            // Board层特定的缓冲区处理逻辑
            return RC_OK;
        }

  # 头文件修改 - 适合添加声明
  header_modifications:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.h"
      description: "添加Board层方法声明"
      additions:
        - position: "public_methods"
          code: |
            // Board层特定方法
            RetCode ProcessBoardSpecificBuffer(std::shared_ptr<IBuffer>& buffer);

  # 文件patch - 适合简单的文件修改
  file_patches:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/BUILD.gn"
      description: "Board层构建配置修改"
      backup: false
      patch_content: |
        @@ -28,6 +28,7 @@
           deps = [
             "//drivers/peripheral/camera/hal/utils:camera_utils",
             "//third_party/libjpeg-turbo:turbojpeg_static",
        +    "//vendor/custom/board:board_extensions",
           ]
        +  defines = [ "ENABLE_BOARD_EXTENSIONS" ]
         }
