# OpenHarmony Device Code Generator

English | [中文](README.md)

A powerful OpenHarmony device development code generation tool that automatically generates device-specific code structures, build configurations, and driver implementations.

## Project Overview

The OpenHarmony Device Code Generator is an automated code generation tool specifically designed for the OpenHarmony ecosystem. Through a template-based approach, it helps developers quickly generate OpenHarmony-compliant device adaptation code, greatly simplifying device porting and development workload.

### Core Features

- **🚀 Automated Code Generation**: Generate complete device code structures with minimal configuration
- **🏗️ Modular Architecture**: Support for Board-level, SoC-level, and Vendor-level components
- **📝 Template-Driven**: Flexible template system based on Jinja2, supporting high customization
- **🌐 Multi-Language Support**: Generate C/C++, Shell scripts, and configuration files
- **⚙️ Build System Integration**: Automatically generate BUILD.gn, Makefile and other build files
- **🔧 Custom Code Support**: Advanced custom code modification and replacement functionality
- **🆕 Patch System**: Support for incremental file modifications using patch functionality

## Overall Code Architecture

### Architecture Overview

```
OpenHarmony Device Code Generator
├── Core Generator Engine
│   ├── Configuration Parser
│   ├── Template Engine
│   └── File Generator
├── Processor System
│   ├── Board Processors
│   ├── SoC Processors
│   └── Vendor Processors
├── Template System
│   ├── Device Templates
│   └── Vendor Templates
└── Custom Code System
    ├── Code Replacement Engine
    ├── Patch Processor
    └── Custom Config Parser
```

### Directory Structure

```
device_code_generator/
├── src/                          # Source code directory
│   └── oh_codegen/              # Main package
│       ├── processors/          # Component processors
│       │   ├── board/          # Board-level processors (kernel, audio, camera, etc.)
│       │   ├── soc/            # SoC-level processors (GPU, codec, etc.)
│       │   └── vendor/         # Vendor-level processors (product config, etc.)
│       ├── custom_code/        # Custom code processing module
│       ├── utils/              # Utility functions and helper classes
│       └── simplified_generator.py  # Main generator engine
├── template/                    # Template files directory
│   ├── device/                 # Device templates
│   │   ├── board/             # Board-level templates (drivers, configs, etc.)
│   │   └── soc/               # SoC-level templates (hardware abstraction layer, etc.)
│   └── vendor/                # Vendor templates (product customization, etc.)
├── config/                     # Configuration files directory
│   ├── rk3568.yaml            # RK3568 configuration example
│   └── *.custom.yaml          # Custom code configurations
├── output/                     # Generated code output directory
├── docs/                       # Documentation directory
│   └── CUSTOM_CODE_GUIDE.md   # Custom code guide
└── tests/                      # Test files
```

### Three-Layer Architecture Design

The OpenHarmony Device Code Generator adopts a three-layer architecture design that fully complies with OpenHarmony's standard directory structure:

1. **Board Layer**: Hardware board-related drivers and configurations
   - **Functions**: Kernel configuration, audio drivers, camera drivers, configuration files, etc.
   - **Output Path**: `output/{version}/{product}/board/{company}/{product}/`
   - **Typical Components**: kernel, audio_drivers, camera, cfg, distributedhardware

2. **SoC Layer**: Chip platform-related components
   - **Functions**: GPU, codecs, display controllers, media processing, etc.
   - **Output Path**: `output/{version}/{product}/soc/{soc_company}/{soc}/`
   - **Typical Components**: gpu, mpp, rga, omx_il, codec, display

3. **Vendor Layer**: Product and vendor-specific configurations
   - **Functions**: Product configuration, application configuration, security configuration, Bluetooth configuration, etc.
   - **Output Path**: `output/{version}/{product}/vendor/{company}/{product}/`
   - **Typical Components**: bluetooth, security_config, hdf_config, default_app_config

## Quick Start

### Environment Requirements

- Python 3.8 or higher
- OpenHarmony development environment (based on OpenHarmony v5.0.0 TAG version)

### Installation Steps

1. **Clone Repository**:
```bash
git clone <repository-url>
cd device_code_generator
```

2. **Install Dependencies**:

   **Method 1: Direct pip install (Recommended)**
   ```bash
   pip install -e .
   ```

   **Method 2: Manual dependency installation**
   ```bash
   pip install PyYAML>=6.0 Jinja2>=3.0.0
   ```

   **Method 3: Development environment (includes testing tools)**
   ```bash
   pip install -e ".[dev]"
   ```

### Basic Usage

1. **Configure Device Information**: Edit configuration file (e.g., `config/rk3568.yaml`)

2. **Generate Code**:
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml
```

3. **Clean and Regenerate**:
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml --clean
```

4. **View Generated Results**:
```bash
ls -la output/5.0.0/rk3568/
```

## Configuration Guide

### Basic Configuration Structure

```yaml
# Basic device information
product_name: "rk3568"           # Product name
device_company: "hihope"         # Device manufacturer
soc_company: "rockchip"          # SoC manufacturer
soc: "rk3568"                    # SoC model
ohos_version: "5.0.0"            # OpenHarmony version

# Board-level module configuration
board_modules:
  - kernel:                      # Kernel module
      enabled: true
      linux_kernel_version: "5.10"
      if_use_kernel_patch: true
  - audio_drivers:               # Audio drivers
      enabled: true
      adm_enabled: true
      adm_codec_chip: "rk809"
  - camera_drivers:              # Camera drivers
      enabled: true
      nodes: ["exif", "codec"]

# SoC-level component configuration
soc_components:
  - gpu:                         # GPU component
      enabled: true
  - mpp:                         # Media processing platform
      enabled: true
  - rga:                         # Graphics accelerator
      enabled: true
  - omx_il:                      # OpenMAX IL
      enabled: true

# Vendor-level component configuration
vendor_components:
  - bluetooth:                   # Bluetooth configuration
      enabled: true
  - security_config:             # Security configuration
      enabled: true
```

## Generated Code Structure

The generated code will be organized according to OpenHarmony's standard directory structure:

```
output/5.0.0/rk3568/
├── board/hihope/rk3568/           # Board-level code
│   ├── kernel/                    # Kernel configuration
│   ├── audio_drivers/             # Audio drivers
│   ├── camera/                    # Camera drivers
│   └── cfg/                       # Configuration files
├── soc/rockchip/rk3568/           # SoC-level code
│   ├── gpu/                       # GPU drivers
│   ├── mpp/                       # Media processing
│   └── rga/                       # Graphics accelerator
└── vendor/hihope/rk3568/          # Vendor-level code
    ├── bluetooth/                 # Bluetooth configuration
    └── security_config/           # Security configuration
```

### View Generated Results

```bash
# View generated directory structure
tree output/5.0.0/rk3568/

# View development guide
cat output/5.0.0/rk3568/board/hihope/DEVELOPMENT_GUIDE.md
```

## Advanced Features

### Custom Code Modification

For advanced code customization and replacement functionality, please refer to our detailed guides:
**[Custom Code Guide](docs/CUSTOM_CODE_GUIDE.md)**

### 🆕 Patch Functionality

The new patch functionality supports incremental modifications to any file, including BUILD.gn, JSON configuration files, etc.:
**[Patch Functionality Guide](docs/PATCH_FUNCTIONALITY_GUIDE.md)**

#### Quick Experience with Patch Functionality

```bash
# Run patch functionality demo
python demo_patch_functionality.py

# Run patch functionality tests
python tests/test_patch_functionality.py

# Use patch generation tool
python -m src.oh_codegen.tools.patch_generator --help
```

This guide includes:
- Creating and configuring custom code configuration files
- Defining code replacement rules
- Conditional replacement and batch replacement
- Practical use cases and best practices

#### Template Customization

To customize templates, you can:

1. **Modify existing templates**: Edit `.j2` files in the `template/` directory
2. **Add new templates**: Create new template files and corresponding processors
3. **Use Jinja2 syntax**: Support conditional rendering, loop generation, variable substitution, etc.

#### Processor Extension

Create custom processors:

```python
from ..base_processor import BaseBoardProcessor

class MyCustomProcessor(BaseBoardProcessor):
    def process(self, config, output_dir):
        # Implement custom processing logic
        pass
```

## Project Configuration Files

The project uses modern Python package management with the following configuration files:

### pyproject.toml (Main Configuration)
- **Purpose**: Standard configuration file for modern Python projects
- **Contains**: Project metadata, dependency management, build configuration, development tool configuration
- **Dependencies**: PyYAML>=6.0, Jinja2>=3.0.0
- **Installation**: `pip install -e .`

### setup.py (Compatibility Support)
- **Purpose**: Traditional setuptools configuration for backward compatibility
- **Function**: Reads configuration from setup.cfg for package building
- **Usage**: `python setup.py install` (not recommended)

### setup.cfg (Traditional Configuration)
- **Purpose**: Traditional configuration file containing basic package information
- **Content**: Package name, version, Python version requirements
- **Status**: Mainly for compatibility, recommend using pyproject.toml

**Recommended Usage**: Prioritize using `pip install -e .` based on pyproject.toml for installation

## Output Structure

The generated code strictly follows OpenHarmony's standard directory structure:

```
output/5.0.0/rk3568/
├── board/hihope/rk3568/         # Board-level code
│   ├── kernel/                  # Kernel-related files
│   │   ├── BUILD.gn            # Kernel build file
│   │   ├── build_kernel.sh     # Kernel compilation script
│   │   └── config/             # Kernel configuration
│   ├── audio_drivers/           # Audio drivers
│   │   ├── codec/              # Audio codec
│   │   ├── dai/                # Digital Audio Interface
│   │   └── BUILD.gn            # Audio build file
│   ├── camera/                  # Camera drivers
│   └── BUILD.gn                 # Board-level main build file
├── soc/rockchip/rk3568/         # SoC-level code
│   ├── gpu/                     # GPU component
│   │   ├── include/            # GPU header files
│   │   ├── lib/                # 32-bit GPU libraries
│   │   ├── lib64/              # 64-bit GPU libraries
│   │   └── BUILD.gn            # GPU build file
│   ├── mpp/                     # Media Processing Platform
│   │   ├── include/            # MPP header files
│   │   ├── lib/                # MPP library files
│   │   └── BUILD.gn            # MPP build file
│   └── BUILD.gn                 # SoC-level main build file
└── vendor/hihope/rk3568/        # Vendor-level code
    ├── bluetooth/               # Bluetooth configuration
    ├── security_config/         # Security configuration
    ├── config.json             # Product configuration file
    └── BUILD.gn                # Vendor-level build file
```

## FAQ

### Q: How to add support for a new chip?
**A**:
1. Copy an existing configuration file (e.g., `rk3568.yaml`)
2. Modify chip-related parameters (`soc`, `soc_company`, etc.)
3. Adjust component configuration according to new chip characteristics
4. Create chip-specific templates if needed

### Q: Can the generated code be compiled directly?
**A**: The generated code provides a complete build framework and basic implementation, but may need adjustments and improvements based on specific hardware characteristics.

### Q: How to add new component types?
**A**:
1. Create component templates under `template/device/`
2. Create corresponding processors under `src/oh_codegen/processors/`
3. Register new components in the processor registry
4. Add component configurations to the configuration file
### Q: Which OpenHarmony versions are supported?
**A**: Currently, OpenHarmony 5.0.0 is primarily supported. This can be adjusted via the `ohos_version` parameter in the configuration file.

## Contribution Guide

1. Fork this repository
2. Create a feature branch (`git checkout -b feature/xxxxx`)
3. Commit your changes (`git commit -m ‘Add some xxxxx’)
4. Push to the branch (`git push origin feature/xxxxx`)
5. Open a Pull Request

