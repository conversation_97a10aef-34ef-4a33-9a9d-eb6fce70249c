#!/usr/bin/env python3
"""
测试patch功能的可行性
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / 'src'))

from oh_codegen.custom_code.config import CustomCodeConfig, FilePatch
from oh_codegen.custom_code.patch_processor import PatchProcessor


def test_basic_patch_functionality():
    """测试基本的patch功能"""
    print("🧪 测试基本patch功能...")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        test_file = temp_path / "test.cpp"
        original_content = """#include <iostream>

int main() {
    std::cout << "Hello World" << std::endl;
    return 0;
}"""
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # 创建patch
        patch_content = """@@ -2,5 +2,7 @@
 
 int main() {
     std::cout << "Hello World" << std::endl;
+    std::cout << "This is a patch test" << std::endl;
+    // Added by patch
     return 0;
 }"""
        
        # 应用patch
        file_patch = FilePatch(
            file_path="test.cpp",
            patch_content=patch_content,
            description="测试patch",
            backup=False
        )
        
        processor = PatchProcessor(temp_path)
        success = processor.apply_patches([file_patch])
        
        if success:
            # 验证结果
            with open(test_file, 'r', encoding='utf-8') as f:
                result_content = f.read()
            
            expected_content = """#include <iostream>

int main() {
    std::cout << "Hello World" << std::endl;
    std::cout << "This is a patch test" << std::endl;
    // Added by patch
    return 0;
}"""
            
            if result_content.strip() == expected_content.strip():
                print("✅ 基本patch功能测试通过")
                return True
            else:
                print("❌ patch结果不匹配")
                print("期望:")
                print(expected_content)
                print("实际:")
                print(result_content)
                return False
        else:
            print("❌ patch应用失败")
            return False


def test_build_gn_patch():
    """测试BUILD.gn文件的patch功能"""
    print("🧪 测试BUILD.gn文件patch...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建BUILD.gn测试文件
        build_file = temp_path / "BUILD.gn"
        original_content = """import("//build/ohos.gni")

ohos_shared_library("camera_pipeline_core") {
  sources = [
    "src/node/my_capture_node.cpp",
    "src/node/my_preview_node.cpp",
  ]
  
  deps = [
    "//drivers/peripheral/camera/hal/utils:camera_utils",
    "//third_party/libjpeg-turbo:turbojpeg_static",
  ]
}"""
        
        with open(build_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # 创建BUILD.gn patch
        patch_content = """@@ -8,6 +8,7 @@
   deps = [
     "//drivers/peripheral/camera/hal/utils:camera_utils",
     "//third_party/libjpeg-turbo:turbojpeg_static",
+    "//vendor/custom/camera:custom_pipeline_ext",
   ]
+  defines = [ "ENABLE_CUSTOM_PIPELINE" ]
 }"""
        
        file_patch = FilePatch(
            file_path="BUILD.gn",
            patch_content=patch_content,
            description="添加自定义依赖和定义",
            backup=False
        )
        
        processor = PatchProcessor(temp_path)
        success = processor.apply_patches([file_patch])
        
        if success:
            with open(build_file, 'r', encoding='utf-8') as f:
                result_content = f.read()
            
            # 检查是否包含新添加的内容
            if ("custom_pipeline_ext" in result_content and 
                "ENABLE_CUSTOM_PIPELINE" in result_content):
                print("✅ BUILD.gn patch测试通过")
                return True
            else:
                print("❌ BUILD.gn patch结果不正确")
                print("结果内容:")
                print(result_content)
                return False
        else:
            print("❌ BUILD.gn patch应用失败")
            return False


def test_json_config_patch():
    """测试JSON配置文件的patch功能"""
    print("🧪 测试JSON配置文件patch...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建JSON配置文件
        config_file = temp_path / "config.json"
        original_content = """{
  "product_name": "rk3568",
  "camera_config": {
    "preview_resolution": "1920x1080",
    "capture_resolution": "3264x2448"
  }
}"""
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # 创建JSON patch
        patch_content = """@@ -3,5 +3,10 @@
   "camera_config": {
     "preview_resolution": "1920x1080",
     "capture_resolution": "3264x2448"
+  },
+  "custom_config": {
+    "enable_advanced_features": true,
+    "debug_level": 2
   }
 }"""
        
        file_patch = FilePatch(
            file_path="config.json",
            patch_content=patch_content,
            description="添加自定义配置",
            backup=False
        )
        
        processor = PatchProcessor(temp_path)
        success = processor.apply_patches([file_patch])
        
        if success:
            with open(config_file, 'r', encoding='utf-8') as f:
                result_content = f.read()
            
            if ("custom_config" in result_content and 
                "enable_advanced_features" in result_content):
                print("✅ JSON配置patch测试通过")
                return True
            else:
                print("❌ JSON配置patch结果不正确")
                return False
        else:
            print("❌ JSON配置patch应用失败")
            return False


def test_config_loading():
    """测试配置加载功能"""
    print("🧪 测试配置加载功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试配置文件
        config_file = temp_path / "test.custom.yaml"
        config_content = """version: "1.0"
product_name: "test_product"

code_modifications:
  function_replacements:
    - file_path: "test.cpp"
      function_signature: "int test()"
      replacement_code: "int test() { return 42; }"
      description: "测试函数替换"
  
  file_patches:
    - file_path: "BUILD.gn"
      description: "测试BUILD.gn patch"
      backup: false
      patch_content: |
        @@ -1,3 +1,4 @@
         deps = [
           "//base:base",
        +  "//custom:custom",
         ]"""
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        # 加载配置
        config = CustomCodeConfig.load_from_file(config_file)
        
        if config:
            if (len(config.function_replacements) == 1 and 
                len(config.file_patches) == 1 and
                config.product_name == "test_product"):
                print("✅ 配置加载测试通过")
                return True
            else:
                print("❌ 配置内容不正确")
                return False
        else:
            print("❌ 配置加载失败")
            return False


def main():
    """运行所有测试"""
    print("🚀 开始测试patch功能可行性...\n")
    
    tests = [
        test_basic_patch_functionality,
        test_build_gn_patch,
        test_json_config_patch,
        test_config_loading,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！patch功能可行性验证成功")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
