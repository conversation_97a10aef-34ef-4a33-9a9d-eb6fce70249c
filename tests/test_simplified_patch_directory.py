#!/usr/bin/env python3
"""
测试简化的patch目录结构功能

测试新的简化patches目录结构（无meta.yaml，支持子目录custom.yaml）
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from oh_codegen.custom_code.config import CustomCodeConfig
from oh_codegen.custom_code.patch_processor import PatchProcessor


def test_simplified_directory_loading():
    """测试简化的目录结构加载"""
    print("🧪 测试简化的目录结构加载...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录结构
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        board_dir = product_dir / "board"
        soc_dir = product_dir / "soc"
        vendor_dir = product_dir / "vendor"
        
        for dir_path in [patches_dir, product_dir, board_dir, soc_dir, vendor_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建board层的custom.yaml
        board_custom_content = """
version: "1.0"
product_name: "test_product"

code_modifications:
  function_replacements:
    - file_path: "test/board/file.cpp"
      function_signature: "int test()"
      replacement_code: "int test() { return 42; }"
      description: "Board层函数替换"
  
  file_patches:
    - file_path: "test/board/BUILD.gn"
      description: "Board层BUILD.gn修改"
      patch_content: |
        @@ -1,3 +1,4 @@
         deps = [
           "//base:base",
        +  "//board:board_ext",
         ]
"""
        
        with open(board_dir / "custom.yaml", 'w', encoding='utf-8') as f:
            f.write(board_custom_content)
        
        # 创建vendor层的patch文件
        vendor_patch = """--- a/test/vendor/config.json
+++ b/test/vendor/config.json
@@ -1,3 +1,4 @@
 {
   "name": "test",
+  "vendor": "custom"
 }"""
        
        with open(vendor_dir / "001-vendor-config.patch", 'w', encoding='utf-8') as f:
            f.write(vendor_patch)
        
        # 测试加载配置
        config = CustomCodeConfig.load_from_patches_directory(patches_dir, "test_product")
        
        if config:
            if (config.product_name == "test_product" and 
                len(config.function_replacements) == 1 and
                len(config.file_patches) == 2):  # 1 from board custom.yaml + 1 from vendor patch
                print("✅ 简化目录结构加载测试通过")
                return True
            else:
                print(f"❌ 配置内容不正确: product={config.product_name}, "
                      f"func_replacements={len(config.function_replacements)}, "
                      f"patches={len(config.file_patches)}")
                return False
        else:
            print("❌ 简化目录结构加载失败")
            return False


def test_layer_priority():
    """测试层级优先级（board -> soc -> vendor）"""
    print("🧪 测试层级优先级...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录结构
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        
        for layer in ["board", "soc", "vendor"]:
            layer_dir = product_dir / layer
            layer_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建patch文件，用于测试顺序
            patch_content = f"""--- a/test/{layer}/file.cpp
+++ b/test/{layer}/file.cpp
@@ -1,3 +1,4 @@
 int main() {{
+    // {layer} layer patch
     return 0;
 }}"""
            
            with open(layer_dir / f"001-{layer}-test.patch", 'w', encoding='utf-8') as f:
                f.write(patch_content)
        
        # 测试加载配置
        config = CustomCodeConfig.load_from_patches_directory(patches_dir, "test_product")
        
        if config and len(config.file_patches) == 3:
            # 检查顺序：应该是board, soc, vendor
            expected_order = ["board", "soc", "vendor"]
            actual_order = []
            
            for patch in config.file_patches:
                for layer in expected_order:
                    if f"test/{layer}/" in patch.file_path:
                        actual_order.append(layer)
                        break
            
            if actual_order == expected_order:
                print("✅ 层级优先级测试通过")
                return True
            else:
                print(f"❌ 层级顺序不正确: expected={expected_order}, actual={actual_order}")
                return False
        else:
            print(f"❌ 配置加载失败或patch数量不正确: {len(config.file_patches) if config else 0}")
            return False


def test_mixed_config_types():
    """测试混合配置类型（custom.yaml + .patch文件）"""
    print("🧪 测试混合配置类型...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录结构
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        board_dir = product_dir / "board"
        vendor_dir = product_dir / "vendor"
        
        for dir_path in [patches_dir, product_dir, board_dir, vendor_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # board层使用custom.yaml（支持函数替换）
        board_custom_content = """
version: "1.0"
product_name: "test_product"

code_modifications:
  function_replacements:
    - file_path: "test/board/manager.cpp"
      function_signature: "int Manager::init()"
      replacement_code: |
        int Manager::init() {
            // Custom board initialization
            return 0;
        }
      description: "Board层管理器初始化"
"""
        
        with open(board_dir / "custom.yaml", 'w', encoding='utf-8') as f:
            f.write(board_custom_content)
        
        # vendor层使用.patch文件（简单修改）
        vendor_patch = """--- a/test/vendor/config.json
+++ b/test/vendor/config.json
@@ -1,3 +1,4 @@
 {
   "name": "test",
+  "custom": true
 }"""
        
        with open(vendor_dir / "001-config.patch", 'w', encoding='utf-8') as f:
            f.write(vendor_patch)
        
        # 测试加载配置
        config = CustomCodeConfig.load_from_patches_directory(patches_dir, "test_product")
        
        if config:
            if (len(config.function_replacements) == 1 and 
                len(config.file_patches) == 1 and
                config.function_replacements[0].description == "Board层管理器初始化"):
                print("✅ 混合配置类型测试通过")
                return True
            else:
                print(f"❌ 混合配置内容不正确: func_replacements={len(config.function_replacements)}, "
                      f"patches={len(config.file_patches)}")
                return False
        else:
            print("❌ 混合配置加载失败")
            return False


def test_priority_system():
    """测试优先级系统（patches目录 > custom.yaml文件）"""
    print("🧪 测试优先级系统...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        board_dir = product_dir / "board"
        
        for dir_path in [patches_dir, product_dir, board_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建patches目录中的配置
        board_custom_content = """
version: "1.0"
product_name: "test_product"

code_modifications:
  function_replacements:
    - file_path: "test/patches_version.cpp"
      function_signature: "int test()"
      replacement_code: "int test() { return 1; }"
      description: "Patches目录版本"
"""
        
        with open(board_dir / "custom.yaml", 'w', encoding='utf-8') as f:
            f.write(board_custom_content)
        
        # 创建传统的config目录和文件
        config_dir = temp_path / "config"
        config_dir.mkdir(exist_ok=True)
        
        traditional_custom_content = """
version: "1.0"
product_name: "test_product"

code_modifications:
  function_replacements:
    - file_path: "test/traditional_version.cpp"
      function_signature: "int test()"
      replacement_code: "int test() { return 2; }"
      description: "传统配置版本"
"""
        
        with open(config_dir / "test_product.custom.yaml", 'w', encoding='utf-8') as f:
            f.write(traditional_custom_content)
        
        # 测试优先级：patches目录应该优先
        config = CustomCodeConfig.find_patches_config(temp_path, "test_product")
        
        if config:
            if (len(config.function_replacements) == 1 and 
                "patches_version.cpp" in config.function_replacements[0].file_path):
                print("✅ 优先级系统测试通过 - patches目录优先")
                return True
            else:
                print("❌ 优先级系统测试失败 - 应该优先使用patches目录")
                return False
        else:
            print("❌ 优先级系统测试失败 - 无法加载配置")
            return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行简化patch目录结构功能测试...")
    print("=" * 60)
    
    tests = [
        test_simplified_directory_loading,
        test_layer_priority,
        test_mixed_config_types,
        test_priority_system,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！简化patch目录结构功能正常工作")
        print("\n✨ 新功能特点:")
        print("- 无需meta.yaml文件，使用固定顺序：board -> soc -> vendor")
        print("- 支持子目录custom.yaml文件，方便函数替换")
        print("- 支持.patch文件，适合简单修改")
        print("- 保持向后兼容，patches目录优先于传统配置")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
