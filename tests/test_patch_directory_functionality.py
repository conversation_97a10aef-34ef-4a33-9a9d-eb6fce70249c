#!/usr/bin/env python3
"""
测试patch目录结构化功能

测试新的patches目录结构和元数据配置功能
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from oh_codegen.custom_code.config import CustomCodeConfig, PatchMetadata, PatchLayerConfig
from oh_codegen.custom_code.patch_processor import PatchProcessor


def test_patch_metadata_loading():
    """测试patch元数据加载功能"""
    print("🧪 测试patch元数据加载功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试用的meta.yaml文件
        meta_content = """
version: "1.0"
product_name: "test_product"
description: "测试产品配置"

apply_order:
  - "board"
  - "soc"
  - "vendor"

global_settings:
  backup: false
  stop_on_error: true
  skip_missing_files: false

layers:
  board:
    description: "Board层测试"
    enabled: true
    backup: false
    priority: 1
  soc:
    description: "SoC层测试"
    enabled: true
    backup: false
    priority: 2
  vendor:
    description: "Vendor层测试"
    enabled: true
    backup: true
    priority: 3

dependencies:
  - layer: "soc"
    depends_on: ["board"]
  - layer: "vendor"
    depends_on: ["board", "soc"]
"""
        
        meta_file = temp_path / "meta.yaml"
        with open(meta_file, 'w', encoding='utf-8') as f:
            f.write(meta_content)
        
        # 测试加载元数据
        metadata = CustomCodeConfig._load_patch_metadata(meta_file)
        
        if metadata:
            if (metadata.product_name == "test_product" and 
                len(metadata.apply_order) == 3 and
                len(metadata.layers) == 3 and
                metadata.layers["board"].priority == 1):
                print("✅ 元数据加载测试通过")
                return True
            else:
                print("❌ 元数据内容不正确")
                return False
        else:
            print("❌ 元数据加载失败")
            return False


def test_patch_directory_loading():
    """测试从patches目录加载配置"""
    print("🧪 测试从patches目录加载配置...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录结构
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        board_dir = product_dir / "board"
        soc_dir = product_dir / "soc"
        vendor_dir = product_dir / "vendor"
        
        for dir_path in [patches_dir, product_dir, board_dir, soc_dir, vendor_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建meta.yaml
        meta_content = """
version: "1.0"
product_name: "test_product"
apply_order: ["board", "soc", "vendor"]
global_settings:
  backup: false
  stop_on_error: true
layers:
  board:
    enabled: true
    backup: false
  soc:
    enabled: true
    backup: false
  vendor:
    enabled: true
    backup: true
"""
        
        with open(product_dir / "meta.yaml", 'w', encoding='utf-8') as f:
            f.write(meta_content)
        
        # 创建测试patch文件
        board_patch = """--- a/test/board/file.cpp
+++ b/test/board/file.cpp
@@ -1,3 +1,4 @@
 int main() {
+    // Board patch applied
     return 0;
 }"""
        
        soc_patch = """--- a/test/soc/file.cpp
+++ b/test/soc/file.cpp
@@ -1,3 +1,4 @@
 int main() {
+    // SoC patch applied
     return 0;
 }"""
        
        with open(board_dir / "001-board-test.patch", 'w', encoding='utf-8') as f:
            f.write(board_patch)
        
        with open(soc_dir / "001-soc-test.patch", 'w', encoding='utf-8') as f:
            f.write(soc_patch)
        
        # 测试加载配置
        config = CustomCodeConfig.load_from_patches_directory(patches_dir, "test_product")
        
        if config:
            if (config.product_name == "test_product" and 
                len(config.file_patches) == 2):
                print("✅ Patches目录加载测试通过")
                return True
            else:
                print(f"❌ 配置内容不正确: product={config.product_name}, patches={len(config.file_patches)}")
                return False
        else:
            print("❌ Patches目录加载失败")
            return False


def test_patch_priority_system():
    """测试patch配置优先级系统"""
    print("🧪 测试patch配置优先级系统...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建patches目录
        patches_dir = temp_path / "patches"
        product_dir = patches_dir / "test_product"
        board_dir = product_dir / "board"
        
        for dir_path in [patches_dir, product_dir, board_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
        
        # 创建meta.yaml
        meta_content = """
version: "1.0"
product_name: "test_product"
apply_order: ["board"]
"""
        
        with open(product_dir / "meta.yaml", 'w', encoding='utf-8') as f:
            f.write(meta_content)
        
        # 创建patch文件
        patch_content = """--- a/test/file.cpp
+++ b/test/file.cpp
@@ -1,3 +1,4 @@
 int main() {
+    // Patch applied
     return 0;
 }"""
        
        with open(board_dir / "001-test.patch", 'w', encoding='utf-8') as f:
            f.write(patch_content)
        
        # 创建传统的custom.yaml文件
        config_dir = temp_path / "config"
        config_dir.mkdir(exist_ok=True)
        
        custom_yaml_content = """
version: "1.0"
product_name: "test_product"
code_modifications:
  file_patches:
    - file_path: "test/custom.cpp"
      description: "Custom YAML patch"
      patch_content: |
        @@ -1,3 +1,4 @@
         int main() {
        +    // Custom YAML patch
             return 0;
         }
"""
        
        with open(config_dir / "test_product.custom.yaml", 'w', encoding='utf-8') as f:
            f.write(custom_yaml_content)
        
        # 测试优先级：patches目录应该优先于custom.yaml
        config = CustomCodeConfig.find_patches_config(temp_path, "test_product")
        
        if config:
            # 应该从patches目录加载，而不是custom.yaml
            if len(config.file_patches) == 1 and "test/file.cpp" in config.file_patches[0].file_path:
                print("✅ 优先级系统测试通过 - patches目录优先")
                return True
            else:
                print("❌ 优先级系统测试失败 - 应该优先使用patches目录")
                return False
        else:
            print("❌ 优先级系统测试失败 - 无法加载配置")
            return False


def test_patch_validation():
    """测试patch验证功能"""
    print("🧪 测试patch验证功能...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # 创建测试文件
        test_file = temp_path / "test.cpp"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("int main() {\n    return 0;\n}\n")
        
        # 创建有效的patch
        from oh_codegen.custom_code.config import FilePatch
        
        valid_patch = FilePatch(
            file_path="test.cpp",
            patch_content="""--- a/test.cpp
+++ b/test.cpp
@@ -1,3 +1,4 @@
 int main() {
+    // Valid patch
     return 0;
 }""",
            description="Valid patch test"
        )
        
        # 创建无效的patch
        invalid_patch = FilePatch(
            file_path="nonexistent.cpp",
            patch_content="invalid patch content",
            description="Invalid patch test"
        )
        
        # 测试验证功能
        processor = PatchProcessor(temp_path)
        
        # 验证有效patch
        valid_errors = processor.validate_patches([valid_patch])
        if len(valid_errors) == 0:
            print("✅ 有效patch验证通过")
        else:
            print(f"❌ 有效patch验证失败: {valid_errors}")
            return False
        
        # 验证无效patch
        invalid_errors = processor.validate_patches([invalid_patch])
        if len(invalid_errors) > 0:
            print("✅ 无效patch验证通过")
            return True
        else:
            print("❌ 无效patch验证失败 - 应该检测到错误")
            return False


def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行patch目录结构化功能测试...")
    print("=" * 60)
    
    tests = [
        test_patch_metadata_loading,
        test_patch_directory_loading,
        test_patch_priority_system,
        test_patch_validation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！patch目录结构化功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
