"""
SoC Processor Module

Provides processors for SoC-level components:
- Display processors: Handle display controller components
- GPU processors: Handle graphics processing unit components
- Codec processors: Handle codec components
- OMX IL processors: Handle OpenMAX IL components
- Build config processors: Handle SoC build configuration
- Generic processors: Handle other SoC components

Each processor is responsible for handling code generation logic for specific SoC component types.
"""

from .display_processor import DisplayProcessor
from .gpu_processor import GPUProcessor
from .codec_processor import CodecProcessor
from .omx_il_processor import OMXILProcessor
from .mpp_processor import MPPProcessor
from .rga_processor import RGAProcessor
from .build_config_processor import SoCBuildConfigProcessor
from .generic_processor import SoCGenericProcessor

# SoC processor registry
SOC_PROCESSORS = {
    'display': DisplayProcessor,
    'gpu': GPUProcessor,
    'codec': CodecProcessor,
    'omx_il': OMXILProcessor,
    'mpp': MPPProcessor,
    'rga': RGAProcessor,
    'build_config': SoCBuildConfigProcessor,
    'my_group': SoCGenericProcessor,  # Generic processor for custom modules
}

def get_soc_processor(component_type: str):
    """
    Get processor for specified SoC component type

    Args:
        component_type: SoC component type name

    Returns:
        Corresponding processor class

    Raises:
        ValueError: If component type is not supported
    """
    if component_type not in SOC_PROCESSORS:
        raise ValueError(f"Unsupported SoC component type: {component_type}")

    return SOC_PROCESSORS[component_type]

def list_supported_soc_components():
    """Get list of supported SoC component types"""
    return list(SOC_PROCESSORS.keys())

__all__ = [
    'DisplayProcessor',
    'GPUProcessor',
    'CodecProcessor',
    'OMXILProcessor',
    'MPPProcessor',
    'RGAProcessor',
    'SoCBuildConfigProcessor',
    'SoCGenericProcessor',
    'get_soc_processor',
    'list_supported_soc_components',
    'SOC_PROCESSORS'
]
