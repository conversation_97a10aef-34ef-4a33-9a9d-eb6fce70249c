"""
RGA SoC Processor

Handles RGA (Rockchip Graphics Accelerator) code generation for SoC-level components.
"""

import logging
from pathlib import Path
from typing import Dict, Any

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class RGAProcessor(BaseBoardProcessor):
    """RGA SoC processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether RGA component should be processed

        Args:
            config: RGA configuration

        Returns:
            True if RGA is enabled, False otherwise
        """
        return config.get("enabled", True)  # Default enabled for SoC components
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        Get output directory name
        
        Args:
            config: RGA configuration
            
        Returns:
            Output directory name
        """
        return "rga"
    
    def get_template_directory_name(self) -> str:
        """
        Get template directory name

        Returns:
            Template directory name
        """
        return "rga"
    
    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        Process RGA code generation with additional directories

        Args:
            config: RGA configuration
            output_base_dir: Output base directory

        Returns:
            Processing result
        """
        result = ProcessResult()

        try:
            # Check if should process
            if not self.should_process(config):
                logger.info("RGA component not enabled, skipping processing")
                result.success = True
                return result

            # Get output directory
            output_dir_name = self.get_output_directory_name(config)
            output_dir = output_base_dir / output_dir_name

            # Get template directory
            template_base_path = self.get_template_base_path()
            template_dir_name = self.get_template_directory_name()
            device_template_dir = self.template_dir / "device" / template_base_path / template_dir_name

            if not device_template_dir.exists():
                result.add_error(f"Template directory does not exist: {device_template_dir}")
                return result

            logger.info(f"Processing RGA: {template_dir_name} -> {output_dir_name}")

            # Process template directory
            self._process_device_directory(device_template_dir, output_dir, result)

            # Create additional directories based on OpenHarmony structure
            self._create_additional_directories(output_dir, result)

            # Generate module report
            if len(result.errors) == 0:
                # Generate Chinese report
                report_content_zh = self._load_report_template(device_template_dir, config, 'zh')
                result.set_module_report(output_dir_name, report_content_zh)

                # Generate English report
                report_content_en = self._load_report_template(device_template_dir, config, 'en')
                result.set_module_report_en(output_dir_name, report_content_en)

            result.success = len(result.errors) == 0
            return result

        except Exception as e:
            result.add_error(f"Error processing RGA: {e}")
            return result

    def _create_additional_directories(self, output_dir: Path, result: ProcessResult):
        """
        Create additional directories for RGA component
        
        Args:
            output_dir: Output directory
            result: Processing result
        """
        try:
            # Create directories based on OpenHarmony RGA structure
            additional_dirs = [
                "include",
                "lib", 
                "lib64"
            ]

            for dir_name in additional_dirs:
                dir_path = output_dir / dir_name
                dir_path.mkdir(parents=True, exist_ok=True)
                result.output_directories.append(str(dir_path))
                logger.info(f"Created RGA directory: {dir_path}")

        except Exception as e:
            result.add_error(f"Error creating additional RGA directories: {e}")
