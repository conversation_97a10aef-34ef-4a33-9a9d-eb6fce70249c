"""
Camera Device Processor

Handles camera-related code generation, supporting different camera chips and node configurations.
"""

from pathlib import Path
from typing import Dict, Any
import logging

from ..base_processor import BaseBoardProcessor, ProcessResult

logger = logging.getLogger(__name__)


class CameraProcessor(BaseBoardProcessor):
    """Camera Board Device Processor"""

    def should_process(self, config: Dict[str, Any]) -> bool:
        """
        Determine whether camera device should be processed

        Args:
            config: Camera device configuration

        Returns:
            True if camera device is enabled, False otherwise
        """
        return config.get('enabled', False)
    
    def get_output_directory_name(self, config: Dict[str, Any]) -> str:
        """
        获取输出目录名称
        
        Args:
            config: 摄像头设备配置
            
        Returns:
            输出目录名称
        """
        # 摄像头统一输出到camera目录
        return 'camera/vdi_impl/v4l2'
    
    def get_template_directory_name(self) -> str:
        """
        获取模板目录名称
        
        Returns:
            模板目录名称
        """
        return 'camera'
    
    def process(self, config: Dict[str, Any], output_base_dir: Path) -> ProcessResult:
        """
        处理摄像头设备代码生成
        
        Args:
            config: 摄像头设备配置
            output_base_dir: 输出基础目录
            
        Returns:
            处理结果
        """
        result = ProcessResult()
        
        try:
            # 检查是否应该处理
            if not self.should_process(config):
                logger.info("Camera device not enabled, skipping processing")
                result.success = True
                return result

            # 分析摄像头配置
            camera_info = self._analyze_camera_config(config)
            logger.info(f"Camera configuration: {camera_info}")

            # 设置摄像头特定的模板变量
            self.template_variables['camera_method'] = camera_info['method']
            self.template_variables['is_usb_camera'] = camera_info['is_usb_camera']

            # 只有在非USB模式下才设置sensor变量
            if not camera_info['is_usb_camera']:
                camera_chip = camera_info['camera_chip']
                if camera_chip:
                    self.template_variables['sensor'] = camera_chip
                    logger.info(f"Set sensor variable: {camera_chip}")
            else:
                logger.info("USB camera mode: skipping sensor variable setup")

            # 获取输出目录
            output_dir = output_base_dir / self.get_output_directory_name(config)
            template_dir = self.template_dir / "device" / "board" / self.get_template_directory_name()
            
            if not template_dir.exists():
                result.add_error(f"摄像头模板目录不存在: {template_dir}")
                return result
            
            logger.info(f"Processing camera: {template_dir} -> {output_dir}")
            
            # 处理模板目录
            self._process_device_directory(template_dir, output_dir, result)
            
            # 根据摄像头芯片类型进行特殊处理
            self._process_camera_specific_logic(config, output_dir, result)

            # 生成模块报告（中文和英文版本）
            if len(result.errors) == 0:
                # 生成中文报告
                report_content_zh = self._load_report_template(template_dir, config, 'zh')
                result.set_module_report(self.get_output_directory_name(config), report_content_zh)

                # 生成英文报告
                report_content_en = self._load_report_template(template_dir, config, 'en')
                result.set_module_report_en(self.get_output_directory_name(config), report_content_en)

            result.success = len(result.errors) == 0
            return result
            
        except Exception as e:
            result.add_error(f"处理摄像头设备时出错: {e}")
            return result
    
    def _analyze_camera_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析摄像头配置

        Args:
            config: 摄像头设备配置

        Returns:
            分析结果
        """
        method = config.get('method', 'v4l2')  # 默认为v4l2模式

        camera_info = {
            'camera_chip': config.get('camera_chip', ''),
            'nodes': config.get('node', []),
            'enabled': config.get('enabled', False),
            'method': method,
            'is_usb_camera': method == 'usb'
        }

        # 如果是USB相机模式，忽略sensor和node选项
        if camera_info['is_usb_camera']:
            logger.info("USB camera mode detected, ignoring sensor and node options")
            camera_info['camera_chip'] = ''  # 清空sensor
            camera_info['nodes'] = []        # 清空nodes

        logger.debug(f"摄像头配置分析: {camera_info}")
        return camera_info
    
    def _process_camera_specific_logic(self, config: Dict[str, Any], output_dir: Path, result: ProcessResult):
        """
        处理摄像头特定的逻辑

        Args:
            config: 摄像头设备配置
            output_dir: 输出目录
            result: 处理结果
        """
        try:
            method = config.get('method', 'v4l2')
            is_usb_camera = method == 'usb'

            if is_usb_camera:
                logger.info("USB camera mode: skipping sensor and node processing")
                return

            # 只有在非USB模式下才处理sensor和nodes
            camera_chip = config.get('camera_chip', '')
            nodes = config.get('node', [])

            # 可以在这里添加摄像头特定的处理逻辑
            # 例如：根据不同的摄像头芯片生成不同的配置文件

            if camera_chip:
                logger.info(f"Processing camera chip: {camera_chip}")
                # Can add specific chip processing logic here

            if nodes:
                logger.info(f"Processing camera nodes: {nodes}")
                self._process_camera_nodes(nodes, output_dir, result)

        except Exception as e:
            result.add_warning(f"处理摄像头特定逻辑时出错: {e}")

    def _process_camera_nodes(self, nodes: list, output_dir: Path, result: ProcessResult):
        """
        处理摄像头节点，为每个节点生成对应的文件

        Args:
            nodes: 节点列表
            output_dir: 输出目录
            result: 结果累积器
        """
        try:
            # 节点文件输出到pipeline_core/src/node目录
            node_output_dir = output_dir / "pipeline_core" / "src" / "node"
            node_template_dir = self.template_dir / "device" / "board" / "camera" / "pipeline_core" / "src" / "node"

            if not node_template_dir.exists():
                logger.warning(f"节点模板目录不存在: {node_template_dir}")
                return

            # 为每个节点生成文件
            for node_name in nodes:
                logger.info(f"Processing camera node: {node_name}")
                self._process_single_node(node_name, node_template_dir, node_output_dir, result)

        except Exception as e:
            result.add_error(f"处理摄像头节点时出错: {e}")

    def _process_single_node(self, node_name: str, template_dir: Path, output_dir: Path, result: ProcessResult):
        """
        处理单个摄像头节点

        Args:
            node_name: 节点名称
            template_dir: 模板目录
            output_dir: 输出目录
            result: 结果累积器
        """
        try:
            # 确保输出目录存在
            output_dir.mkdir(parents=True, exist_ok=True)

            # 设置节点特定的模板变量
            original_vars = self.template_variables.copy()
            self.template_variables['node_name'] = node_name

            # 处理节点模板文件
            for template_file in template_dir.glob("my_{{node_name}}_node.*"):
                if template_file.suffix == '.j2':
                    self._process_template_file(template_file, output_dir, result)

            # 恢复原始模板变量
            self.template_variables = original_vars

        except Exception as e:
            result.add_error(f"处理节点 {node_name} 时出错: {e}")

    def _process_device_directory(self, template_dir: Path, output_dir: Path, result: ProcessResult):
        """
        重写基类方法，跳过node模板文件的处理

        Args:
            template_dir: 模板目录
            output_dir: 输出目录
            result: 结果累积器
        """
        try:
            # 1. 首先检查并执行copy_file.sh脚本
            copy_script = template_dir / "copy_file.sh"
            if copy_script.exists() and copy_script.stat().st_size > 0:
                # 传递项目根目录参数
                project_root = self.template_dir.parent  # 从template目录向上一级到项目根目录
                self._execute_script(copy_script, result, project_root)

            # 2. 处理当前目录中的模板文件
            for item in template_dir.iterdir():
                if item.is_file() and item.suffix == '.j2':
                    # 跳过node模板文件，这些文件由专门的node处理逻辑处理
                    if 'my_{{node_name}}_node' in item.name:
                        continue
                    # 跳过报告模板文件，这些文件会在后续单独处理
                    if item.name in ['report.md.j2', 'report_en.md.j2']:
                        continue
                    self._process_template_file(item, output_dir, result)
                elif item.is_dir():
                    # 递归处理子目录
                    sub_output_dir = output_dir / item.name
                    self._process_device_directory(item, sub_output_dir, result)

        except Exception as e:
            result.add_error(f"处理摄像头目录 {template_dir} 时出错: {e}")


# 注意：如果将来需要支持多种摄像头框架（如V4L2、HDI等），
# 可以通过扩展CameraProcessor类来实现不同的处理器
