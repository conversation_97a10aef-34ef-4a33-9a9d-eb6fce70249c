#!/usr/bin/env python3
"""
Patch应用API

提供独立的patch应用功能，支持在Python代码或命令行中精确控制patch应用过程
"""

import logging
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import sys

from .custom_code.config import CustomCodeConfig, FilePatch
from .custom_code.code_modifier import CodeModifier
from .custom_code.patch_processor import PatchProcessor

logger = logging.getLogger(__name__)


class PatchApplicator:
    """独立的Patch应用器"""
    
    def __init__(self, project_root: Optional[Path] = None):
        """
        初始化Patch应用器
        
        Args:
            project_root: 项目根目录，默认为当前目录
        """
        self.project_root = project_root or Path.cwd()
        self.code_modifier = CodeModifier(self.project_root)
        self.patch_processor = PatchProcessor(self.project_root)
    
    def apply_custom_yaml(self, custom_yaml_path: Union[str, Path], 
                         dry_run: bool = False, verbose: bool = False) -> Dict[str, Any]:
        """
        应用custom.yaml文件
        
        Args:
            custom_yaml_path: custom.yaml文件路径
            dry_run: 是否为干运行模式
            verbose: 是否输出详细信息
            
        Returns:
            应用结果字典
        """
        try:
            custom_yaml_path = Path(custom_yaml_path)
            if not custom_yaml_path.is_absolute():
                custom_yaml_path = self.project_root / custom_yaml_path
            
            if verbose:
                logger.info(f"正在应用custom.yaml文件: {custom_yaml_path}")
            
            # 加载配置
            config = CustomCodeConfig.load_from_file(custom_yaml_path)
            if not config:
                return {
                    "success": False,
                    "error": f"无法加载配置文件: {custom_yaml_path}",
                    "applied_patches": 0,
                    "applied_functions": 0
                }
            
            result = {
                "success": True,
                "error": None,
                "applied_patches": 0,
                "applied_functions": 0,
                "applied_additions": 0,
                "applied_headers": 0,
                "details": []
            }
            
            if dry_run:
                if verbose:
                    logger.info("=== 干运行模式 - 预览应用结果 ===")
                
                # 预览将要应用的修改
                result["details"].append(f"将要应用的修改:")
                result["details"].append(f"- 函数替换: {len(config.function_replacements)} 个")
                result["details"].append(f"- 函数添加: {len(config.function_additions)} 个")
                result["details"].append(f"- 头文件修改: {len(config.header_modifications)} 个")
                result["details"].append(f"- 文件patch: {len(config.file_patches)} 个")
                
                for replacement in config.function_replacements:
                    result["details"].append(f"  函数替换: {replacement.file_path} -> {replacement.function_signature}")
                
                for patch in config.file_patches:
                    result["details"].append(f"  文件patch: {patch.file_path} ({patch.description})")
                
                return result
            
            # 实际应用修改
            success = self.code_modifier.apply_modifications(config)
            
            if success:
                result["applied_functions"] = len(config.function_replacements)
                result["applied_additions"] = len(config.function_additions)
                result["applied_headers"] = len(config.header_modifications)
                result["applied_patches"] = len(config.file_patches)
                result["details"].append(f"成功应用custom.yaml: {custom_yaml_path}")
                
                if verbose:
                    logger.info(f"✅ 成功应用custom.yaml文件: {custom_yaml_path}")
                    logger.info(f"   - 函数替换: {result['applied_functions']} 个")
                    logger.info(f"   - 函数添加: {result['applied_additions']} 个")
                    logger.info(f"   - 头文件修改: {result['applied_headers']} 个")
                    logger.info(f"   - 文件patch: {result['applied_patches']} 个")
            else:
                result["success"] = False
                result["error"] = "应用custom.yaml文件时发生错误"
                if verbose:
                    logger.error(f"❌ 应用custom.yaml文件失败: {custom_yaml_path}")
            
            return result
            
        except Exception as e:
            error_msg = f"应用custom.yaml文件时发生异常: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "applied_patches": 0,
                "applied_functions": 0
            }
    
    def apply_patch_file(self, patch_file_path: Union[str, Path], 
                        target_file_path: Optional[Union[str, Path]] = None,
                        backup: bool = False, dry_run: bool = False, 
                        verbose: bool = False) -> Dict[str, Any]:
        """
        应用单个.patch文件
        
        Args:
            patch_file_path: patch文件路径
            target_file_path: 目标文件路径（可选，会从patch内容中自动提取）
            backup: 是否创建备份
            dry_run: 是否为干运行模式
            verbose: 是否输出详细信息
            
        Returns:
            应用结果字典
        """
        try:
            patch_file_path = Path(patch_file_path)
            if not patch_file_path.is_absolute():
                patch_file_path = self.project_root / patch_file_path
            
            if verbose:
                logger.info(f"正在应用patch文件: {patch_file_path}")
            
            # 读取patch内容
            with open(patch_file_path, 'r', encoding='utf-8') as f:
                patch_content = f.read()
            
            # 提取目标文件路径
            if target_file_path:
                target_file = str(target_file_path)
            else:
                target_file = CustomCodeConfig._extract_target_file_from_patch(patch_content)
                if not target_file:
                    return {
                        "success": False,
                        "error": f"无法从patch文件中提取目标文件路径: {patch_file_path}",
                        "applied_patches": 0
                    }
            
            # 创建FilePatch对象
            file_patch = FilePatch(
                file_path=target_file,
                patch_content=patch_content,
                description=f"Patch from {patch_file_path.name}",
                backup=backup
            )
            
            result = {
                "success": True,
                "error": None,
                "applied_patches": 0,
                "target_file": target_file,
                "details": []
            }
            
            if dry_run:
                if verbose:
                    logger.info("=== 干运行模式 - 预览patch应用 ===")
                
                result["details"].append(f"将要应用的patch:")
                result["details"].append(f"- Patch文件: {patch_file_path}")
                result["details"].append(f"- 目标文件: {target_file}")
                result["details"].append(f"- 备份: {'是' if backup else '否'}")
                
                # 验证patch
                errors = self.patch_processor.validate_patches([file_patch])
                if errors:
                    result["success"] = False
                    result["error"] = f"Patch验证失败: {'; '.join(errors)}"
                    result["details"].extend(errors)
                else:
                    result["details"].append("✅ Patch验证通过")
                
                return result
            
            # 实际应用patch
            success = self.patch_processor._apply_single_patch(file_patch)
            
            if success:
                result["applied_patches"] = 1
                result["details"].append(f"成功应用patch: {patch_file_path} -> {target_file}")
                
                if verbose:
                    logger.info(f"✅ 成功应用patch文件: {patch_file_path}")
                    logger.info(f"   - 目标文件: {target_file}")
                    logger.info(f"   - 备份: {'是' if backup else '否'}")
            else:
                result["success"] = False
                result["error"] = f"应用patch文件失败: {patch_file_path}"
                if verbose:
                    logger.error(f"❌ 应用patch文件失败: {patch_file_path}")
            
            return result
            
        except Exception as e:
            error_msg = f"应用patch文件时发生异常: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "applied_patches": 0
            }
    
    def apply_patches_directory(self, patches_dir: Union[str, Path], product_name: str,
                               layer: Optional[str] = None, dry_run: bool = False, 
                               verbose: bool = False) -> Dict[str, Any]:
        """
        应用patches目录中的配置
        
        Args:
            patches_dir: patches目录路径
            product_name: 产品名称
            layer: 指定层级（board/soc/vendor），None表示应用所有层级
            dry_run: 是否为干运行模式
            verbose: 是否输出详细信息
            
        Returns:
            应用结果字典
        """
        try:
            patches_dir = Path(patches_dir)
            if not patches_dir.is_absolute():
                patches_dir = self.project_root / patches_dir
            
            product_dir = patches_dir / product_name
            if not product_dir.exists():
                return {
                    "success": False,
                    "error": f"产品目录不存在: {product_dir}",
                    "applied_patches": 0,
                    "applied_functions": 0
                }
            
            if verbose:
                logger.info(f"正在应用patches目录: {product_dir}")
            
            # 确定要应用的层级
            layers = [layer] if layer else ["board", "soc", "vendor"]
            
            result = {
                "success": True,
                "error": None,
                "applied_patches": 0,
                "applied_functions": 0,
                "applied_additions": 0,
                "applied_headers": 0,
                "layers_processed": [],
                "details": []
            }
            
            for layer_name in layers:
                layer_dir = product_dir / layer_name
                if not layer_dir.exists():
                    if verbose:
                        logger.debug(f"层级目录不存在，跳过: {layer_dir}")
                    continue
                
                if verbose:
                    logger.info(f"处理 {layer_name} 层级...")
                
                # 应用该层级的配置
                layer_result = self._apply_layer_config(layer_dir, layer_name, dry_run, verbose)
                
                # 合并结果
                if layer_result["success"]:
                    result["applied_patches"] += layer_result.get("applied_patches", 0)
                    result["applied_functions"] += layer_result.get("applied_functions", 0)
                    result["applied_additions"] += layer_result.get("applied_additions", 0)
                    result["applied_headers"] += layer_result.get("applied_headers", 0)
                    result["layers_processed"].append(layer_name)
                    result["details"].extend(layer_result.get("details", []))
                else:
                    result["success"] = False
                    result["error"] = layer_result.get("error", f"{layer_name}层应用失败")
                    result["details"].extend(layer_result.get("details", []))
                    break
            
            if result["success"] and verbose:
                logger.info(f"✅ 成功应用patches目录: {product_dir}")
                logger.info(f"   - 处理层级: {', '.join(result['layers_processed'])}")
                logger.info(f"   - 总计: 函数{result['applied_functions']}个, patch{result['applied_patches']}个")
            
            return result
            
        except Exception as e:
            error_msg = f"应用patches目录时发生异常: {e}"
            logger.error(error_msg)
            return {
                "success": False,
                "error": error_msg,
                "applied_patches": 0,
                "applied_functions": 0
            }
    
    def _apply_layer_config(self, layer_dir: Path, layer_name: str, 
                           dry_run: bool = False, verbose: bool = False) -> Dict[str, Any]:
        """应用单个层级的配置"""
        result = {
            "success": True,
            "error": None,
            "applied_patches": 0,
            "applied_functions": 0,
            "applied_additions": 0,
            "applied_headers": 0,
            "details": []
        }
        
        # 优先处理custom.yaml文件
        custom_yaml = layer_dir / "custom.yaml"
        if custom_yaml.exists():
            yaml_result = self.apply_custom_yaml(custom_yaml, dry_run, verbose)
            if yaml_result["success"]:
                result["applied_functions"] += yaml_result.get("applied_functions", 0)
                result["applied_additions"] += yaml_result.get("applied_additions", 0)
                result["applied_headers"] += yaml_result.get("applied_headers", 0)
                result["applied_patches"] += yaml_result.get("applied_patches", 0)
                result["details"].extend(yaml_result.get("details", []))
            else:
                result["success"] = False
                result["error"] = yaml_result.get("error")
                result["details"].extend(yaml_result.get("details", []))
                return result
        
        # 处理.patch文件
        patch_files = list(layer_dir.glob("*.patch")) + list(layer_dir.glob("*.diff"))
        patch_files.sort(key=lambda x: x.name)
        
        for patch_file in patch_files:
            patch_result = self.apply_patch_file(
                patch_file, 
                backup=(layer_name == "vendor"),  # vendor层默认备份
                dry_run=dry_run, 
                verbose=verbose
            )
            
            if patch_result["success"]:
                result["applied_patches"] += patch_result.get("applied_patches", 0)
                result["details"].extend(patch_result.get("details", []))
            else:
                result["success"] = False
                result["error"] = patch_result.get("error")
                result["details"].extend(patch_result.get("details", []))
                break
        
        return result


# 便捷函数
def apply_custom_yaml(custom_yaml_path: Union[str, Path], project_root: Optional[Path] = None,
                     dry_run: bool = False, verbose: bool = False) -> Dict[str, Any]:
    """
    便捷函数：应用custom.yaml文件
    
    Args:
        custom_yaml_path: custom.yaml文件路径
        project_root: 项目根目录
        dry_run: 是否为干运行模式
        verbose: 是否输出详细信息
        
    Returns:
        应用结果字典
    """
    applicator = PatchApplicator(project_root)
    return applicator.apply_custom_yaml(custom_yaml_path, dry_run, verbose)


def apply_patch_file(patch_file_path: Union[str, Path], project_root: Optional[Path] = None,
                    target_file_path: Optional[Union[str, Path]] = None, backup: bool = False,
                    dry_run: bool = False, verbose: bool = False) -> Dict[str, Any]:
    """
    便捷函数：应用单个patch文件
    
    Args:
        patch_file_path: patch文件路径
        project_root: 项目根目录
        target_file_path: 目标文件路径（可选）
        backup: 是否创建备份
        dry_run: 是否为干运行模式
        verbose: 是否输出详细信息
        
    Returns:
        应用结果字典
    """
    applicator = PatchApplicator(project_root)
    return applicator.apply_patch_file(patch_file_path, target_file_path, backup, dry_run, verbose)


def apply_patches_directory(patches_dir: Union[str, Path], product_name: str,
                           project_root: Optional[Path] = None, layer: Optional[str] = None,
                           dry_run: bool = False, verbose: bool = False) -> Dict[str, Any]:
    """
    便捷函数：应用patches目录
    
    Args:
        patches_dir: patches目录路径
        product_name: 产品名称
        project_root: 项目根目录
        layer: 指定层级（可选）
        dry_run: 是否为干运行模式
        verbose: 是否输出详细信息
        
    Returns:
        应用结果字典
    """
    applicator = PatchApplicator(project_root)
    return applicator.apply_patches_directory(patches_dir, product_name, layer, dry_run, verbose)
