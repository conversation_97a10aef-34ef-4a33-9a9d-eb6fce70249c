"""
Code Modifier

Used to execute custom code replacement and addition operations
"""

import re
from typing import List, Dict, Optional
from pathlib import Path
import logging

from .config import CustomCodeConfig, FunctionReplacement, FunctionAddition, HeaderModification, FilePatch
from .cpp_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .patch_processor import PatchProcessor

logger = logging.getLogger(__name__)


class CodeModifier:
    """Code modifier"""

    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.parser = CppParser()
        self.patch_processor = PatchProcessor(project_root)

    def apply_modifications(self, config: CustomCodeConfig) -> bool:
        """
        Apply all code modifications

        Args:
            config: Custom code configuration

        Returns:
            Whether all modifications were successfully applied
        """
        success = True

        try:
            logger.info("Starting to apply custom code modifications...")

            # Apply function replacements
            for replacement in config.function_replacements:
                if not self._apply_function_replacement(replacement):
                    success = False

            # Apply function additions
            for addition in config.function_additions:
                if not self._apply_function_addition(addition):
                    success = False

            # Apply header file modifications
            for header_mod in config.header_modifications:
                if not self._apply_header_modification(header_mod):
                    success = False

            # Apply file patches
            if config.file_patches:
                if not self.patch_processor.apply_patches(config.file_patches):
                    success = False

            if success:
                logger.info("All custom code modifications applied successfully")
            else:
                logger.warning("Some custom code modifications failed to apply")

            return success

        except Exception as e:
            logger.error(f"Error applying custom code modifications: {e}")
            return False

    def _apply_function_replacement(self, replacement: FunctionReplacement) -> bool:
        """应用函数替换"""
        try:
            file_path = self.project_root / replacement.file_path
            
            if not file_path.exists():
                logger.warning(f"要替换的文件不存在: {file_path}")
                return False
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找函数定义位置
            func_location = self.parser.find_function_definition(content, replacement.function_signature)
            if not func_location:
                logger.warning(f"未找到要替换的函数: {replacement.function_signature} in {file_path}")
                return False
            
            start_line, end_line = func_location
            lines = content.split('\n')
            
            # 获取原函数的缩进
            indentation = self.parser.get_indentation(content, start_line)
            
            # 准备替换代码（保持缩进）
            replacement_lines = replacement.replacement_code.strip().split('\n')
            indented_replacement = []
            for i, line in enumerate(replacement_lines):
                if i == 0:
                    # 第一行保持原有缩进
                    indented_replacement.append(indentation + line.lstrip())
                else:
                    # 其他行添加相同缩进
                    indented_replacement.append(indentation + line)
            
            # 替换函数
            new_lines = lines[:start_line] + indented_replacement + lines[end_line + 1:]
            new_content = '\n'.join(new_lines)
            
            # 添加包含文件
            if replacement.includes:
                new_content = self._add_includes(new_content, replacement.includes)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"成功替换函数: {replacement.function_signature} in {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"替换函数时出错: {e}")
            return False

    def _apply_function_addition(self, addition: FunctionAddition) -> bool:
        """应用函数添加"""
        try:
            file_path = self.project_root / addition.file_path
            
            if not file_path.exists():
                logger.warning(f"要添加函数的文件不存在: {file_path}")
                return False
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            insert_line = self._find_insertion_point(content, addition.position)
            
            if insert_line is None:
                logger.warning(f"未找到插入位置: {addition.position} in {file_path}")
                return False
            
            # 获取插入位置的缩进
            indentation = self._get_appropriate_indentation(content, insert_line, addition.position)
            
            # 准备添加的代码
            addition_lines = addition.code.strip().split('\n')
            indented_addition = []
            for line in addition_lines:
                if line.strip():  # 非空行添加缩进
                    indented_addition.append(indentation + line.lstrip())
                else:  # 空行保持空行
                    indented_addition.append('')
            
            # 插入代码
            new_lines = lines[:insert_line] + [''] + indented_addition + [''] + lines[insert_line:]
            new_content = '\n'.join(new_lines)
            
            # 添加包含文件
            if addition.includes:
                new_content = self._add_includes(new_content, addition.includes)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"成功添加函数到: {file_path} at position {addition.position}")
            return True
            
        except Exception as e:
            logger.error(f"添加函数时出错: {e}")
            return False

    def _apply_header_modification(self, header_mod: HeaderModification) -> bool:
        """应用头文件修改"""
        try:
            file_path = self.project_root / header_mod.file_path
            
            if not file_path.exists():
                logger.warning(f"要修改的头文件不存在: {file_path}")
                return False
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            new_content = content
            
            # 应用所有添加
            for addition in header_mod.additions:
                position = addition.get('position', 'end_of_file')
                code = addition.get('code', '')
                
                if not code:
                    continue
                
                insert_line = self._find_insertion_point(new_content, position)
                if insert_line is None:
                    logger.warning(f"未找到插入位置: {position} in {file_path}")
                    continue
                
                lines = new_content.split('\n')
                indentation = self._get_appropriate_indentation(new_content, insert_line, position)
                
                # 插入代码
                indented_code = indentation + code.lstrip()
                lines.insert(insert_line, indented_code)
                new_content = '\n'.join(lines)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            logger.info(f"成功修改头文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"修改头文件时出错: {e}")
            return False

    def _find_insertion_point(self, content: str, position: str) -> Optional[int]:
        """查找插入点"""
        lines = content.split('\n')
        
        if position == "end_of_file":
            return len(lines)
        elif position == "end_of_class":
            # 查找最后一个类的结束位置
            for i in range(len(lines) - 1, -1, -1):
                if '}' in lines[i] and 'class' not in lines[i]:
                    return i
            return len(lines)
        elif position.startswith("after_function:"):
            func_name = position.split(":", 1)[1]
            # 查找函数结束位置
            for i, line in enumerate(lines):
                if func_name in line and '{' in line:
                    # 找到函数结束的大括号
                    brace_count = 1
                    for j in range(i + 1, len(lines)):
                        brace_count += lines[j].count('{') - lines[j].count('}')
                        if brace_count == 0:
                            return j + 1
            return None
        elif position.startswith("before_function:"):
            func_name = position.split(":", 1)[1]
            for i, line in enumerate(lines):
                if func_name in line:
                    return i
            return None
        elif position in ["public_methods", "private_members", "protected_methods"]:
            # 在类的相应部分末尾插入
            return self._find_class_section_end(content, position)
        
        return None

    def _find_class_section_end(self, content: str, section: str) -> Optional[int]:
        """查找类部分的结束位置"""
        lines = content.split('\n')
        
        # 简化实现：查找相应的访问修饰符后的最后一个声明
        section_map = {
            "public_methods": "public:",
            "private_members": "private:",
            "protected_methods": "protected:"
        }
        
        target_section = section_map.get(section)
        if not target_section:
            return None
        
        in_section = False
        last_declaration = None
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            if line_stripped == target_section:
                in_section = True
                continue
            elif line_stripped in ["public:", "private:", "protected:"] and in_section:
                # 进入其他部分，返回上一个声明的位置
                break
            elif in_section and (';' in line or '{' in line):
                last_declaration = i
        
        return last_declaration + 1 if last_declaration is not None else None

    def _get_appropriate_indentation(self, content: str, line_number: int, position: str) -> str:
        """获取合适的缩进"""
        lines = content.split('\n')
        
        if line_number < len(lines):
            # 使用目标行的缩进
            return self.parser.get_indentation(content, line_number)
        else:
            # 使用文件中最常见的缩进
            return "    "  # 默认4个空格

    def _add_includes(self, content: str, includes: List[str]) -> str:
        """添加包含文件"""
        if not includes:
            return content
        
        lines = content.split('\n')
        include_end = self.parser.find_includes_section(content)
        
        # 检查是否已存在这些包含文件
        existing_includes = set()
        for line in lines[:include_end + 5]:  # 检查前面几行
            if line.strip().startswith('#include'):
                existing_includes.add(line.strip())
        
        # 添加新的包含文件
        new_includes = []
        for include in includes:
            include = include.strip()
            if include not in existing_includes:
                new_includes.append(include)
        
        if new_includes:
            lines = lines[:include_end] + new_includes + lines[include_end:]
        
        return '\n'.join(lines)
