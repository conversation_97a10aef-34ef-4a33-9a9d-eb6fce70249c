"""
Custom Code Configuration Data Structures

Used to manage custom modification configurations after code generation
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from pathlib import Path
import yaml
import logging
import glob

logger = logging.getLogger(__name__)


@dataclass
class FunctionReplacement:
    """Function replacement configuration"""
    file_path: str
    function_signature: str
    replacement_code: str
    includes: List[str] = field(default_factory=list)
    description: str = ""


@dataclass
class FunctionAddition:
    """Function addition configuration"""
    file_path: str
    position: str  # "end_of_class", "after_function:func_name", "before_function:func_name", "end_of_file"
    code: str
    includes: List[str] = field(default_factory=list)
    description: str = ""


@dataclass
class HeaderModification:
    """Header file modification configuration"""
    file_path: str
    additions: List[Dict[str, str]] = field(default_factory=list)  # [{"position": "public_methods", "code": "..."}]
    description: str = ""


@dataclass
class FilePatch:
    """File patch configuration using unified diff format"""
    file_path: str
    patch_content: str  # Unified diff format patch content
    description: str = ""
    backup: bool = False  # Whether to create backup before applying patch





@dataclass
class CustomCodeConfig:
    """Custom code configuration"""
    version: str = "1.0"
    product_name: str = ""
    function_replacements: List[FunctionReplacement] = field(default_factory=list)
    function_additions: List[FunctionAddition] = field(default_factory=list)
    header_modifications: List[HeaderModification] = field(default_factory=list)
    file_patches: List[FilePatch] = field(default_factory=list)  # New patch support

    @classmethod
    def load_from_file(cls, config_path: Path) -> Optional['CustomCodeConfig']:
        """从YAML文件加载配置"""
        try:
            if not config_path.exists():
                logger.info(f"自定义代码配置文件不存在: {config_path}")
                return None

            with open(config_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)

            if not data:
                logger.warning(f"自定义代码配置文件为空: {config_path}")
                return None

            config = cls()
            config.version = data.get('version', '1.0')
            config.product_name = data.get('product_name', '')

            # 加载函数替换配置
            code_mods = data.get('code_modifications', {})
            
            for replacement_data in code_mods.get('function_replacements', []):
                replacement = FunctionReplacement(
                    file_path=replacement_data['file_path'],
                    function_signature=replacement_data['function_signature'],
                    replacement_code=replacement_data['replacement_code'],
                    includes=replacement_data.get('includes', []),
                    description=replacement_data.get('description', '')
                )
                config.function_replacements.append(replacement)

            # 加载函数添加配置
            for addition_data in code_mods.get('function_additions', []):
                addition = FunctionAddition(
                    file_path=addition_data['file_path'],
                    position=addition_data['position'],
                    code=addition_data['code'],
                    includes=addition_data.get('includes', []),
                    description=addition_data.get('description', '')
                )
                config.function_additions.append(addition)

            # 加载头文件修改配置
            for header_data in code_mods.get('header_modifications', []):
                header_mod = HeaderModification(
                    file_path=header_data['file_path'],
                    additions=header_data.get('additions', []),
                    description=header_data.get('description', '')
                )
                config.header_modifications.append(header_mod)

            # 加载文件patch配置
            for patch_data in code_mods.get('file_patches', []):
                file_patch = FilePatch(
                    file_path=patch_data['file_path'],
                    patch_content=patch_data['patch_content'],
                    description=patch_data.get('description', ''),
                    backup=patch_data.get('backup', False)
                )
                config.file_patches.append(file_patch)

            logger.info(f"Successfully loaded custom code configuration: {config_path}")
            logger.info(f"Configuration contains: {len(config.function_replacements)} function replacements, "
                       f"{len(config.function_additions)} function additions, "
                       f"{len(config.header_modifications)} header modifications, "
                       f"{len(config.file_patches)} file patches")

            return config

        except Exception as e:
            logger.error(f"Failed to load custom code configuration: {e}")
            return None

    def save_to_file(self, config_path: Path):
        """Save configuration to YAML file"""
        try:
            data = {
                'version': self.version,
                'product_name': self.product_name,
                'code_modifications': {
                    'function_replacements': [],
                    'function_additions': [],
                    'header_modifications': [],
                    'file_patches': []
                }
            }

            # 转换函数替换配置
            for replacement in self.function_replacements:
                data['code_modifications']['function_replacements'].append({
                    'file_path': replacement.file_path,
                    'function_signature': replacement.function_signature,
                    'replacement_code': replacement.replacement_code,
                    'includes': replacement.includes,
                    'description': replacement.description
                })

            # 转换函数添加配置
            for addition in self.function_additions:
                data['code_modifications']['function_additions'].append({
                    'file_path': addition.file_path,
                    'position': addition.position,
                    'code': addition.code,
                    'includes': addition.includes,
                    'description': addition.description
                })

            # 转换头文件修改配置
            for header_mod in self.header_modifications:
                data['code_modifications']['header_modifications'].append({
                    'file_path': header_mod.file_path,
                    'additions': header_mod.additions,
                    'description': header_mod.description
                })

            # 转换文件patch配置
            for file_patch in self.file_patches:
                data['code_modifications']['file_patches'].append({
                    'file_path': file_patch.file_path,
                    'patch_content': file_patch.patch_content,
                    'description': file_patch.description,
                    'backup': file_patch.backup
                })

            # 确保目录存在
            config_path.parent.mkdir(parents=True, exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(data, f, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"Custom code configuration saved: {config_path}")

        except Exception as e:
            logger.error(f"Failed to save custom code configuration: {e}")
            raise

    @classmethod
    def load_from_patches_directory(cls, patches_dir: Path, product_name: str) -> Optional['CustomCodeConfig']:
        """从patches目录加载配置（简化版本，无需meta.yaml）"""
        try:
            product_dir = patches_dir / product_name
            if not product_dir.exists():
                logger.info(f"Patches目录不存在: {product_dir}")
                return None

            # 创建配置对象
            config = cls()
            config.version = "1.0"
            config.product_name = product_name

            # 固定的应用顺序：board -> soc -> vendor
            layer_order = ["board", "soc", "vendor"]

            for layer_name in layer_order:
                layer_dir = product_dir / layer_name
                if not layer_dir.exists():
                    logger.debug(f"Layer目录不存在，跳过: {layer_dir}")
                    continue

                # 加载该层的配置
                layer_config = cls._load_layer_config(layer_dir, layer_name)
                if layer_config:
                    # 合并配置
                    config.function_replacements.extend(layer_config.function_replacements)
                    config.function_additions.extend(layer_config.function_additions)
                    config.header_modifications.extend(layer_config.header_modifications)
                    config.file_patches.extend(layer_config.file_patches)

            logger.info(f"Successfully loaded patches configuration from directory: {product_dir}")
            logger.info(f"Configuration contains: {len(config.function_replacements)} function replacements, "
                       f"{len(config.function_additions)} function additions, "
                       f"{len(config.header_modifications)} header modifications, "
                       f"{len(config.file_patches)} file patches")

            return config

        except Exception as e:
            logger.error(f"Failed to load patches configuration: {e}")
            return None

    @classmethod
    def _load_layer_config(cls, layer_dir: Path, layer_name: str) -> Optional['CustomCodeConfig']:
        """加载单个层级的配置"""
        try:
            # 优先查找custom.yaml文件
            custom_yaml = layer_dir / "custom.yaml"
            if custom_yaml.exists():
                logger.info(f"Loading {layer_name} layer config from custom.yaml")
                return cls.load_from_file(custom_yaml)

            # 如果没有custom.yaml，查找.patch文件
            patch_files = []
            for ext in ['.patch', '.diff']:
                patch_files.extend(layer_dir.glob(f'*{ext}'))

            if patch_files:
                logger.info(f"Loading {layer_name} layer config from {len(patch_files)} patch files")
                config = cls()
                config.version = "1.0"
                config.product_name = ""

                # 按文件名排序
                patch_files.sort(key=lambda x: x.name)

                for patch_file in patch_files:
                    try:
                        with open(patch_file, 'r', encoding='utf-8') as f:
                            patch_content = f.read()

                        # 从patch内容中提取目标文件路径
                        target_file = cls._extract_target_file_from_patch(patch_content)
                        if target_file:
                            file_patch = FilePatch(
                                file_path=target_file,
                                patch_content=patch_content,
                                description=f"Patch from {patch_file.name}",
                                backup=(layer_name == "vendor")  # vendor层默认备份
                            )
                            config.file_patches.append(file_patch)
                            logger.debug(f"Loaded patch: {patch_file.name} -> {target_file}")
                    except Exception as e:
                        logger.error(f"Failed to load patch file {patch_file}: {e}")
                        continue

                return config if config.file_patches else None

            logger.debug(f"No configuration found in {layer_name} layer directory: {layer_dir}")
            return None

        except Exception as e:
            logger.error(f"Failed to load layer config from {layer_dir}: {e}")
            return None



    @classmethod
    def _extract_target_file_from_patch(cls, patch_content: str) -> Optional[str]:
        """从patch内容中提取目标文件路径"""
        try:
            lines = patch_content.strip().split('\n')
            for line in lines:
                if line.startswith('--- a/') or line.startswith('+++ b/'):
                    # 提取文件路径，去掉前缀
                    if line.startswith('--- a/'):
                        return line[6:]  # 去掉 "--- a/"
                    elif line.startswith('+++ b/'):
                        return line[6:]  # 去掉 "+++ b/"

            # 如果没有找到标准格式，尝试其他格式
            for line in lines:
                if line.startswith('---') and not line.startswith('--- a/'):
                    # 可能是其他格式的patch
                    parts = line.split()
                    if len(parts) >= 2:
                        return parts[1]

            return None

        except Exception as e:
            logger.error(f"Failed to extract target file from patch: {e}")
            return None

    @classmethod
    def find_patches_config(cls, project_root: Path, product_name: str) -> Optional['CustomCodeConfig']:
        """查找并加载patch配置，优先级：patches目录 > custom.yaml文件"""

        # 优先尝试从patches目录加载
        patches_dir = project_root / "patches"
        if patches_dir.exists():
            config = cls.load_from_patches_directory(patches_dir, product_name)
            if config:
                logger.info(f"Using patches directory configuration for product: {product_name}")
                return config

        # 回退到传统的custom.yaml文件
        config_dir = project_root / "config"

        # 尝试产品特定的配置文件
        product_config = config_dir / f"{product_name}.custom.yaml"
        if product_config.exists():
            config = cls.load_from_file(product_config)
            if config:
                logger.info(f"Using product-specific custom configuration: {product_config}")
                return config

        # 尝试通用的patch示例配置文件
        patch_example_config = config_dir / "patch_example.custom.yaml"
        if patch_example_config.exists():
            config = cls.load_from_file(patch_example_config)
            if config:
                logger.info(f"Using patch example configuration: {patch_example_config}")
                return config

        logger.info(f"No patch configuration found for product: {product_name}")
        return None
