"""
Patch Processor

Used to apply unified diff patches to files
"""

import os
import re
import shutil
import tempfile
from typing import List, Optional, Tuple
from pathlib import Path
import logging

from .config import FilePatch

logger = logging.getLogger(__name__)


class PatchProcessor:
    """Patch processor for applying unified diff patches"""

    def __init__(self, project_root: Path):
        self.project_root = project_root

    def apply_patches(self, patches: List[FilePatch]) -> bool:
        """
        Apply all file patches

        Args:
            patches: List of file patches to apply

        Returns:
            Whether all patches were successfully applied
        """
        success = True

        try:
            logger.info("Starting to apply file patches...")

            for patch in patches:
                if not self._apply_single_patch(patch):
                    success = False

            if success:
                logger.info("All file patches applied successfully")
            else:
                logger.warning("Some file patches failed to apply")

            return success

        except Exception as e:
            logger.error(f"Error applying file patches: {e}")
            return False

    def apply_patches_with_enhanced_handling(self, patches: List[FilePatch], stop_on_error: bool = True,
                                           skip_missing_files: bool = False) -> bool:
        """
        Apply patches with enhanced error handling

        Args:
            patches: List of file patches to apply
            stop_on_error: Whether to stop on first error
            skip_missing_files: Whether to skip missing target files

        Returns:
            Whether all patches were successfully applied
        """
        success = True
        applied_patches = []

        try:
            logger.info(f"Starting to apply {len(patches)} file patches with enhanced processing...")

            for i, patch in enumerate(patches):
                logger.info(f"Applying patch {i+1}/{len(patches)}: {patch.description or patch.file_path}")

                # Check if target file exists
                file_path = self.project_root / patch.file_path
                if not file_path.exists():
                    if skip_missing_files:
                        logger.warning(f"Target file does not exist, skipping: {file_path}")
                        continue
                    else:
                        logger.error(f"Target file does not exist: {file_path}")
                        if stop_on_error:
                            success = False
                            break
                        else:
                            success = False
                            continue

                # Apply the patch
                patch_success = self._apply_single_patch(patch)

                if patch_success:
                    applied_patches.append(patch)
                    logger.debug(f"Successfully applied patch: {patch.description or patch.file_path}")
                else:
                    logger.error(f"Failed to apply patch: {patch.description or patch.file_path}")
                    success = False

                    if stop_on_error:
                        logger.error("Stopping patch application due to error (stop_on_error=True)")
                        break

            if success:
                logger.info(f"All {len(applied_patches)} file patches applied successfully")
            else:
                logger.warning(f"Applied {len(applied_patches)}/{len(patches)} patches, some failed")

                # Note: Rollback functionality can be added if needed

            return success

        except Exception as e:
            logger.error(f"Error applying file patches with metadata: {e}")
            return False

    def _rollback_patches(self, applied_patches: List[FilePatch]) -> bool:
        """
        Rollback applied patches by restoring from backup files

        Args:
            applied_patches: List of patches that were successfully applied

        Returns:
            Whether rollback was successful
        """
        try:
            logger.info(f"Rolling back {len(applied_patches)} applied patches...")
            rollback_success = True

            for patch in reversed(applied_patches):  # Rollback in reverse order
                try:
                    file_path = self.project_root / patch.file_path
                    backup_path = file_path.with_suffix(file_path.suffix + '.backup')

                    if backup_path.exists():
                        shutil.copy2(backup_path, file_path)
                        logger.debug(f"Rolled back: {file_path}")
                    else:
                        logger.warning(f"Backup file not found for rollback: {backup_path}")
                        rollback_success = False

                except Exception as e:
                    logger.error(f"Failed to rollback patch for {patch.file_path}: {e}")
                    rollback_success = False

            if rollback_success:
                logger.info("Patch rollback completed successfully")
            else:
                logger.warning("Patch rollback completed with some errors")

            return rollback_success

        except Exception as e:
            logger.error(f"Error during patch rollback: {e}")
            return False

    def validate_patches(self, patches: List[FilePatch]) -> List[str]:
        """
        Validate patches before applying them

        Args:
            patches: List of patches to validate

        Returns:
            List of validation error messages (empty if all valid)
        """
        errors = []

        try:
            logger.info(f"Validating {len(patches)} patches...")

            for i, patch in enumerate(patches):
                # Check if target file exists
                file_path = self.project_root / patch.file_path
                if not file_path.exists():
                    errors.append(f"Patch {i+1}: Target file does not exist: {patch.file_path}")
                    continue

                # Validate patch format
                if not self._validate_patch_format(patch.patch_content):
                    errors.append(f"Patch {i+1}: Invalid patch format for {patch.file_path}")
                    continue

                # Try to apply patch in dry-run mode
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        original_content = f.read()

                    patched_content = self._apply_unified_diff(original_content, patch.patch_content)
                    if patched_content is None:
                        errors.append(f"Patch {i+1}: Cannot apply patch to {patch.file_path} (content mismatch)")

                except Exception as e:
                    errors.append(f"Patch {i+1}: Error validating patch for {patch.file_path}: {e}")

            if errors:
                logger.warning(f"Patch validation found {len(errors)} errors")
            else:
                logger.info("All patches validated successfully")

            return errors

        except Exception as e:
            logger.error(f"Error during patch validation: {e}")
            return [f"Validation error: {e}"]

    def _validate_patch_format(self, patch_content: str) -> bool:
        """
        Validate that patch content is in proper unified diff format

        Args:
            patch_content: Patch content to validate

        Returns:
            Whether the patch format is valid
        """
        try:
            lines = patch_content.strip().split('\n')

            # Check for basic unified diff structure
            has_header = False
            has_hunk = False

            for line in lines:
                if line.startswith('---') or line.startswith('+++'):
                    has_header = True
                elif line.startswith('@@') and line.endswith('@@'):
                    has_hunk = True
                    break

            return has_header and has_hunk

        except Exception:
            return False

    def _apply_single_patch(self, patch: FilePatch) -> bool:
        """Apply a single patch to a file"""
        try:
            file_path = self.project_root / patch.file_path
            
            if not file_path.exists():
                logger.warning(f"Target file does not exist: {file_path}")
                return False

            # Create backup if requested
            backup_path = None
            if patch.backup:
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                shutil.copy2(file_path, backup_path)
                logger.info(f"Created backup: {backup_path}")

            # Read original file content
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()

            # Apply patch
            patched_content = self._apply_unified_diff(original_content, patch.patch_content)
            
            if patched_content is None:
                logger.error(f"Failed to apply patch to {file_path}")
                # Restore backup if patch failed
                if backup_path and backup_path.exists():
                    shutil.copy2(backup_path, file_path)
                    logger.info(f"Restored from backup: {backup_path}")
                return False

            # Write patched content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(patched_content)

            logger.info(f"Successfully applied patch to: {file_path}")
            
            # Clean up backup if patch was successful and backup not explicitly requested
            if backup_path and backup_path.exists() and not patch.backup:
                backup_path.unlink()

            return True

        except Exception as e:
            logger.error(f"Error applying patch to {patch.file_path}: {e}")
            return False

    def _apply_unified_diff(self, original_content: str, patch_content: str) -> Optional[str]:
        """
        Apply unified diff patch to content
        
        Args:
            original_content: Original file content
            patch_content: Unified diff patch content
            
        Returns:
            Patched content or None if patch failed
        """
        try:
            lines = original_content.splitlines(keepends=True)
            patch_lines = patch_content.strip().split('\n')
            
            # Parse patch hunks
            hunks = self._parse_patch_hunks(patch_lines)
            
            if not hunks:
                logger.warning("No valid hunks found in patch")
                return None

            # Apply hunks in reverse order to maintain line numbers
            for hunk in reversed(hunks):
                lines = self._apply_hunk(lines, hunk)
                if lines is None:
                    return None

            return ''.join(lines)

        except Exception as e:
            logger.error(f"Error applying unified diff: {e}")
            return None

    def _parse_patch_hunks(self, patch_lines: List[str]) -> List[dict]:
        """Parse patch hunks from unified diff format"""
        hunks = []
        current_hunk = None
        
        for line in patch_lines:
            line = line.rstrip()
            
            # Skip header lines
            if line.startswith('---') or line.startswith('+++'):
                continue
                
            # Hunk header: @@ -old_start,old_count +new_start,new_count @@
            if line.startswith('@@'):
                if current_hunk:
                    hunks.append(current_hunk)
                
                # Parse hunk header
                match = re.match(r'@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@', line)
                if match:
                    old_start = int(match.group(1))
                    old_count = int(match.group(2)) if match.group(2) else 1
                    new_start = int(match.group(3))
                    new_count = int(match.group(4)) if match.group(4) else 1
                    
                    current_hunk = {
                        'old_start': old_start,
                        'old_count': old_count,
                        'new_start': new_start,
                        'new_count': new_count,
                        'lines': []
                    }
                continue
            
            # Hunk content lines
            if current_hunk is not None:
                if line.startswith(' ') or line.startswith('+') or line.startswith('-'):
                    current_hunk['lines'].append(line)
                elif line == '':
                    # Empty line in patch
                    current_hunk['lines'].append(' ')
        
        # Add last hunk
        if current_hunk:
            hunks.append(current_hunk)
            
        return hunks

    def _apply_hunk(self, lines: List[str], hunk: dict) -> Optional[List[str]]:
        """Apply a single hunk to the file lines"""
        try:
            old_start = hunk['old_start'] - 1  # Convert to 0-based index
            old_count = hunk['old_count']
            hunk_lines = hunk['lines']
            
            # Verify context matches
            context_lines = []
            add_lines = []
            remove_count = 0
            
            for hunk_line in hunk_lines:
                if hunk_line.startswith(' '):
                    # Context line
                    context_lines.append(hunk_line[1:])
                elif hunk_line.startswith('-'):
                    # Remove line
                    remove_count += 1
                elif hunk_line.startswith('+'):
                    # Add line
                    add_lines.append(hunk_line[1:])
            
            # Apply the hunk
            result_lines = lines[:old_start]
            
            # Skip removed lines and add new lines
            skip_count = 0
            add_index = 0
            
            for i, hunk_line in enumerate(hunk_lines):
                if hunk_line.startswith(' '):
                    # Context line - should match
                    if old_start + skip_count < len(lines):
                        result_lines.append(lines[old_start + skip_count])
                        skip_count += 1
                elif hunk_line.startswith('-'):
                    # Remove line - skip it
                    skip_count += 1
                elif hunk_line.startswith('+'):
                    # Add line
                    add_line = hunk_line[1:]
                    if not add_line.endswith('\n') and add_line:
                        add_line += '\n'
                    result_lines.append(add_line)
            
            # Add remaining lines
            result_lines.extend(lines[old_start + skip_count:])
            
            return result_lines
            
        except Exception as e:
            logger.error(f"Error applying hunk: {e}")
            return None

    def create_patch(self, file_path: Path, original_content: str, modified_content: str) -> str:
        """
        Create a unified diff patch from original and modified content
        
        Args:
            file_path: Path to the file
            original_content: Original file content
            modified_content: Modified file content
            
        Returns:
            Unified diff patch content
        """
        import difflib
        
        original_lines = original_content.splitlines(keepends=True)
        modified_lines = modified_content.splitlines(keepends=True)
        
        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"a/{file_path}",
            tofile=f"b/{file_path}",
            lineterm=''
        )
        
        return ''.join(diff)
