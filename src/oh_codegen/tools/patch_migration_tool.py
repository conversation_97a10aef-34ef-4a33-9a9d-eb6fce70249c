#!/usr/bin/env python3
"""
Patch迁移工具

用于将现有的custom.yaml文件迁移到新的patches目录结构
"""

import argparse
import sys
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


class PatchMigrationTool:
    """Patch迁移工具类"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.config_dir = project_root / "config"
        self.patches_dir = project_root / "patches"
    
    def migrate_custom_yaml_to_patches(self, custom_yaml_path: Path, product_name: str, 
                                     dry_run: bool = False) -> bool:
        """
        将custom.yaml文件迁移到patches目录结构
        
        Args:
            custom_yaml_path: 现有的custom.yaml文件路径
            product_name: 产品名称
            dry_run: 是否为干运行模式
            
        Returns:
            是否迁移成功
        """
        try:
            logger.info(f"开始迁移 {custom_yaml_path} 到patches目录结构...")
            
            # 加载现有配置
            if not custom_yaml_path.exists():
                logger.error(f"配置文件不存在: {custom_yaml_path}")
                return False
            
            with open(custom_yaml_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            
            if not data:
                logger.error("配置文件为空")
                return False
            
            # 提取配置信息
            version = data.get('version', '1.0')
            original_product_name = data.get('product_name', product_name)
            code_mods = data.get('code_modifications', {})
            file_patches = code_mods.get('file_patches', [])
            
            if not file_patches:
                logger.warning("没有找到file_patches，无需迁移")
                return True
            
            logger.info(f"找到 {len(file_patches)} 个patch需要迁移")
            
            # 创建patches目录结构
            product_dir = self.patches_dir / product_name
            if not dry_run:
                product_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建层级目录
                for layer in ['board', 'soc', 'vendor']:
                    (product_dir / layer).mkdir(exist_ok=True)
            
            # 分析和分类patch
            categorized_patches = self._categorize_patches(file_patches)
            
            # 生成meta.yaml
            meta_config = self._generate_meta_config(version, product_name, categorized_patches)
            
            if dry_run:
                logger.info("=== 干运行模式 - 预览迁移结果 ===")
                self._preview_migration(product_name, categorized_patches, meta_config)
            else:
                # 写入meta.yaml
                meta_file = product_dir / "meta.yaml"
                with open(meta_file, 'w', encoding='utf-8') as f:
                    yaml.dump(meta_config, f, default_flow_style=False, allow_unicode=True, indent=2)
                logger.info(f"创建meta.yaml: {meta_file}")
                
                # 创建patch文件
                self._create_patch_files(product_dir, categorized_patches)
                
                logger.info(f"✅ 迁移完成！新的patches目录: {product_dir}")
                logger.info("请验证迁移结果，确认无误后可以删除原始custom.yaml文件")
            
            return True
            
        except Exception as e:
            logger.error(f"迁移失败: {e}")
            return False
    
    def _categorize_patches(self, file_patches: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据文件路径将patch分类到不同层级
        
        Args:
            file_patches: 原始patch列表
            
        Returns:
            分类后的patch字典
        """
        categorized = {
            'board': [],
            'soc': [],
            'vendor': [],
            'unknown': []
        }
        
        for patch in file_patches:
            file_path = patch.get('file_path', '')
            category = self._determine_patch_category(file_path)
            categorized[category].append(patch)
        
        return categorized
    
    def _determine_patch_category(self, file_path: str) -> str:
        """
        根据文件路径确定patch所属层级
        
        Args:
            file_path: 文件路径
            
        Returns:
            层级名称 ('board', 'soc', 'vendor', 'unknown')
        """
        file_path_lower = file_path.lower()
        
        # 根据路径特征判断层级
        if '/board/' in file_path_lower or 'camera' in file_path_lower or 'audio' in file_path_lower:
            return 'board'
        elif '/soc/' in file_path_lower or 'power' in file_path_lower or 'clock' in file_path_lower:
            return 'soc'
        elif '/vendor/' in file_path_lower or 'config.json' in file_path_lower or 'product' in file_path_lower:
            return 'vendor'
        else:
            return 'unknown'
    
    def _generate_meta_config(self, version: str, product_name: str, 
                            categorized_patches: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        生成meta.yaml配置
        
        Args:
            version: 版本号
            product_name: 产品名称
            categorized_patches: 分类后的patch
            
        Returns:
            meta.yaml配置字典
        """
        # 确定实际使用的层级
        used_layers = []
        for layer in ['board', 'soc', 'vendor']:
            if categorized_patches.get(layer):
                used_layers.append(layer)
        
        # 如果有unknown类别的patch，添加到board层
        if categorized_patches.get('unknown'):
            if 'board' not in used_layers:
                used_layers.insert(0, 'board')
        
        meta_config = {
            'version': version,
            'product_name': product_name,
            'description': f'{product_name}产品的patch配置（从custom.yaml迁移）',
            'apply_order': used_layers,
            'global_settings': {
                'backup': False,
                'stop_on_error': True,
                'skip_missing_files': False,
                'verbose': True
            },
            'layers': {},
            'metadata': {
                'migrated_from': 'custom.yaml',
                'migration_tool': 'patch_migration_tool',
                'migration_date': '2024-01-01'
            }
        }
        
        # 添加层级配置
        layer_descriptions = {
            'board': 'Board层硬件相关patch',
            'soc': 'SoC层芯片相关patch',
            'vendor': 'Vendor层产品定制patch'
        }
        
        for i, layer in enumerate(used_layers, 1):
            meta_config['layers'][layer] = {
                'description': layer_descriptions.get(layer, f'{layer}层patch'),
                'enabled': True,
                'backup': layer == 'vendor',  # vendor层默认备份
                'priority': i
            }
        
        # 添加依赖关系
        if len(used_layers) > 1:
            dependencies = []
            for i, layer in enumerate(used_layers[1:], 1):
                dependencies.append({
                    'layer': layer,
                    'depends_on': used_layers[:i]
                })
            meta_config['dependencies'] = dependencies
        
        return meta_config
    
    def _create_patch_files(self, product_dir: Path, categorized_patches: Dict[str, List[Dict[str, Any]]]):
        """
        创建patch文件
        
        Args:
            product_dir: 产品目录路径
            categorized_patches: 分类后的patch
        """
        for layer, patches in categorized_patches.items():
            if not patches:
                continue
            
            # unknown类别的patch放到board层
            if layer == 'unknown':
                layer = 'board'
            
            layer_dir = product_dir / layer
            
            for i, patch in enumerate(patches, 1):
                # 生成patch文件名
                description = patch.get('description', 'patch')
                safe_description = self._make_safe_filename(description)
                patch_filename = f"{i:03d}-{safe_description}.patch"
                
                # 提取patch内容
                patch_content = patch.get('patch_content', '')
                
                # 写入patch文件
                patch_file = layer_dir / patch_filename
                with open(patch_file, 'w', encoding='utf-8') as f:
                    f.write(patch_content)
                
                logger.info(f"创建patch文件: {patch_file}")
    
    def _make_safe_filename(self, description: str) -> str:
        """
        将描述转换为安全的文件名
        
        Args:
            description: 原始描述
            
        Returns:
            安全的文件名
        """
        # 移除或替换不安全的字符
        safe_chars = []
        for char in description:
            if char.isalnum() or char in '-_':
                safe_chars.append(char)
            elif char in ' \t':
                safe_chars.append('-')
        
        safe_name = ''.join(safe_chars).strip('-')
        
        # 限制长度
        if len(safe_name) > 50:
            safe_name = safe_name[:50].rstrip('-')
        
        return safe_name or 'patch'
    
    def _preview_migration(self, product_name: str, categorized_patches: Dict[str, List[Dict[str, Any]]], 
                          meta_config: Dict[str, Any]):
        """
        预览迁移结果
        
        Args:
            product_name: 产品名称
            categorized_patches: 分类后的patch
            meta_config: meta配置
        """
        print(f"\n📁 将创建的目录结构:")
        print(f"patches/{product_name}/")
        print(f"├── meta.yaml")
        
        for layer in meta_config['apply_order']:
            patches = categorized_patches.get(layer, [])
            if layer == 'board':
                # board层可能包含unknown的patch
                patches.extend(categorized_patches.get('unknown', []))
            
            print(f"├── {layer}/")
            for i, patch in enumerate(patches, 1):
                description = patch.get('description', 'patch')
                safe_description = self._make_safe_filename(description)
                patch_filename = f"{i:03d}-{safe_description}.patch"
                print(f"│   └── {patch_filename}")
        
        print(f"\n📄 meta.yaml内容预览:")
        print(yaml.dump(meta_config, default_flow_style=False, allow_unicode=True, indent=2))
        
        print(f"\n📊 迁移统计:")
        total_patches = sum(len(patches) for patches in categorized_patches.values())
        print(f"- 总patch数量: {total_patches}")
        for layer, patches in categorized_patches.items():
            if patches:
                print(f"- {layer}层: {len(patches)} 个patch")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Patch迁移工具 - 将custom.yaml迁移到patches目录结构')
    parser.add_argument('custom_yaml', help='现有的custom.yaml文件路径')
    parser.add_argument('product_name', help='产品名称')
    parser.add_argument('--project-root', default='.', help='项目根目录路径 (默认: 当前目录)')
    parser.add_argument('--dry-run', action='store_true', help='干运行模式，只预览不实际创建文件')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 初始化工具
    project_root = Path(args.project_root).resolve()
    custom_yaml_path = Path(args.custom_yaml)
    
    if not custom_yaml_path.is_absolute():
        custom_yaml_path = project_root / custom_yaml_path
    
    tool = PatchMigrationTool(project_root)
    
    # 执行迁移
    success = tool.migrate_custom_yaml_to_patches(
        custom_yaml_path, 
        args.product_name, 
        args.dry_run
    )
    
    if success:
        if args.dry_run:
            print("\n✅ 干运行完成！使用 --dry-run=false 执行实际迁移")
        else:
            print("\n🎉 迁移完成！")
            print(f"新的patches目录已创建: patches/{args.product_name}/")
            print("请验证迁移结果，确认无误后可以删除原始custom.yaml文件")
    else:
        print("\n❌ 迁移失败！")
        sys.exit(1)


if __name__ == '__main__':
    main()
