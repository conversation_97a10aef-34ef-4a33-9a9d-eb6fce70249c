#!/usr/bin/env python3
"""
Patch应用命令行工具

提供命令行接口来精确控制patch应用过程
"""

import argparse
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "src"))

from oh_codegen.patch_api import PatchApplicator, apply_custom_yaml, apply_patch_file, apply_patches_directory


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s',
        handlers=[logging.StreamHandler()]
    )


def print_result(result: Dict[str, Any], output_format: str = "text"):
    """打印结果"""
    if output_format == "json":
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return
    
    # 文本格式输出
    if result["success"]:
        print("✅ 操作成功")
        
        # 统计信息
        stats = []
        if result.get("applied_functions", 0) > 0:
            stats.append(f"函数替换: {result['applied_functions']} 个")
        if result.get("applied_additions", 0) > 0:
            stats.append(f"函数添加: {result['applied_additions']} 个")
        if result.get("applied_headers", 0) > 0:
            stats.append(f"头文件修改: {result['applied_headers']} 个")
        if result.get("applied_patches", 0) > 0:
            stats.append(f"文件patch: {result['applied_patches']} 个")
        
        if stats:
            print(f"应用统计: {', '.join(stats)}")
        
        # 处理的层级
        if result.get("layers_processed"):
            print(f"处理层级: {', '.join(result['layers_processed'])}")
        
        # 目标文件
        if result.get("target_file"):
            print(f"目标文件: {result['target_file']}")
        
        # 详细信息
        if result.get("details"):
            print("\n详细信息:")
            for detail in result["details"]:
                print(f"  {detail}")
    else:
        print("❌ 操作失败")
        if result.get("error"):
            print(f"错误: {result['error']}")
        
        if result.get("details"):
            print("\n详细信息:")
            for detail in result["details"]:
                print(f"  {detail}")


def cmd_apply_custom_yaml(args):
    """应用custom.yaml文件"""
    result = apply_custom_yaml(
        custom_yaml_path=args.custom_yaml,
        project_root=Path(args.project_root) if args.project_root else None,
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    print_result(result, args.output)
    return 0 if result["success"] else 1


def cmd_apply_patch(args):
    """应用patch文件"""
    result = apply_patch_file(
        patch_file_path=args.patch_file,
        project_root=Path(args.project_root) if args.project_root else None,
        target_file_path=args.target_file,
        backup=args.backup,
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    print_result(result, args.output)
    return 0 if result["success"] else 1


def cmd_apply_patches_dir(args):
    """应用patches目录"""
    result = apply_patches_directory(
        patches_dir=args.patches_dir,
        product_name=args.product,
        project_root=Path(args.project_root) if args.project_root else None,
        layer=args.layer,
        dry_run=args.dry_run,
        verbose=args.verbose
    )
    
    print_result(result, args.output)
    return 0 if result["success"] else 1


def cmd_list_patches(args):
    """列出可用的patches"""
    try:
        patches_dir = Path(args.patches_dir)
        if not patches_dir.is_absolute():
            project_root_path = Path(args.project_root) if args.project_root else Path.cwd()
            patches_dir = project_root_path / patches_dir
        
        if not patches_dir.exists():
            print(f"❌ Patches目录不存在: {patches_dir}")
            return 1
        
        print(f"📁 Patches目录: {patches_dir}")
        print()
        
        # 列出所有产品
        products = [d for d in patches_dir.iterdir() if d.is_dir()]
        
        if not products:
            print("没有找到任何产品配置")
            return 0
        
        for product_dir in sorted(products):
            product_name = product_dir.name
            print(f"🔸 产品: {product_name}")
            
            # 列出各层级的配置
            for layer in ["board", "soc", "vendor"]:
                layer_dir = product_dir / layer
                if not layer_dir.exists():
                    continue
                
                configs = []
                
                # 检查custom.yaml
                custom_yaml = layer_dir / "custom.yaml"
                if custom_yaml.exists():
                    configs.append("custom.yaml")
                
                # 检查patch文件
                patch_files = list(layer_dir.glob("*.patch")) + list(layer_dir.glob("*.diff"))
                if patch_files:
                    configs.append(f"{len(patch_files)} 个patch文件")
                
                if configs:
                    print(f"   - {layer}: {', '.join(configs)}")
            
            print()
        
        return 0
        
    except Exception as e:
        print(f"❌ 列出patches时发生错误: {e}")
        return 1


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='Patch应用命令行工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 应用custom.yaml文件
  %(prog)s apply-yaml patches/rk3568/board/custom.yaml

  # 应用单个patch文件
  %(prog)s apply-patch patches/rk3568/vendor/001-config.patch --backup

  # 应用整个产品的patches
  %(prog)s apply-dir patches rk3568

  # 只应用特定层级
  %(prog)s apply-dir patches rk3568 --layer board

  # 干运行模式预览
  %(prog)s apply-yaml patches/rk3568/board/custom.yaml --dry-run

  # 列出所有可用的patches
  %(prog)s list patches
        """
    )

    # 全局参数
    parser.add_argument('--project-root', help='项目根目录路径 (默认: 当前目录)')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    parser.add_argument('--dry-run', action='store_true', help='干运行模式，只预览不实际应用')
    parser.add_argument('--output', choices=['text', 'json'], default='text', help='输出格式')

    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')

    # apply-yaml 子命令
    yaml_parser = subparsers.add_parser('apply-yaml', help='应用custom.yaml文件')
    yaml_parser.add_argument('custom_yaml', help='custom.yaml文件路径')
    yaml_parser.add_argument('--project-root', help='项目根目录路径 (默认: 当前目录)')
    yaml_parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    yaml_parser.add_argument('--dry-run', action='store_true', help='干运行模式，只预览不实际应用')
    yaml_parser.add_argument('--output', choices=['text', 'json'], default='text', help='输出格式')
    yaml_parser.set_defaults(func=cmd_apply_custom_yaml)

    # apply-patch 子命令
    patch_parser = subparsers.add_parser('apply-patch', help='应用单个patch文件')
    patch_parser.add_argument('patch_file', help='patch文件路径')
    patch_parser.add_argument('--target-file', help='目标文件路径 (可选，会从patch中自动提取)')
    patch_parser.add_argument('--backup', action='store_true', help='创建备份文件')
    patch_parser.add_argument('--project-root', help='项目根目录路径 (默认: 当前目录)')
    patch_parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    patch_parser.add_argument('--dry-run', action='store_true', help='干运行模式，只预览不实际应用')
    patch_parser.add_argument('--output', choices=['text', 'json'], default='text', help='输出格式')
    patch_parser.set_defaults(func=cmd_apply_patch)

    # apply-dir 子命令
    dir_parser = subparsers.add_parser('apply-dir', help='应用patches目录')
    dir_parser.add_argument('patches_dir', help='patches目录路径')
    dir_parser.add_argument('product', help='产品名称')
    dir_parser.add_argument('--layer', choices=['board', 'soc', 'vendor'],
                           help='指定层级 (默认: 应用所有层级)')
    dir_parser.add_argument('--project-root', help='项目根目录路径 (默认: 当前目录)')
    dir_parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    dir_parser.add_argument('--dry-run', action='store_true', help='干运行模式，只预览不实际应用')
    dir_parser.add_argument('--output', choices=['text', 'json'], default='text', help='输出格式')
    dir_parser.set_defaults(func=cmd_apply_patches_dir)

    # list 子命令
    list_parser = subparsers.add_parser('list', help='列出可用的patches')
    list_parser.add_argument('patches_dir', help='patches目录路径')
    list_parser.add_argument('--project-root', help='项目根目录路径 (默认: 当前目录)')
    list_parser.set_defaults(func=cmd_list_patches)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 执行命令
    try:
        return args.func(args)
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行命令时发生错误: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == '__main__':
    sys.exit(main())
