#!/usr/bin/env python3
"""
Patch Generator Tool

帮助用户生成patch内容的工具
"""

import argparse
import sys
import difflib
from pathlib import Path
from typing import Optional


class PatchGenerator:
    """Patch生成器"""

    def __init__(self):
        pass

    def generate_patch_from_files(self, original_file: Path, modified_file: Path, 
                                 relative_path: Optional[str] = None) -> str:
        """
        从两个文件生成patch
        
        Args:
            original_file: 原始文件路径
            modified_file: 修改后的文件路径
            relative_path: 在patch中显示的相对路径
            
        Returns:
            生成的patch内容
        """
        if not original_file.exists():
            raise FileNotFoundError(f"Original file not found: {original_file}")
        
        if not modified_file.exists():
            raise FileNotFoundError(f"Modified file not found: {modified_file}")

        # 读取文件内容
        with open(original_file, 'r', encoding='utf-8') as f:
            original_lines = f.readlines()
        
        with open(modified_file, 'r', encoding='utf-8') as f:
            modified_lines = f.readlines()

        # 生成patch
        if relative_path is None:
            relative_path = str(original_file)

        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"a/{relative_path}",
            tofile=f"b/{relative_path}",
            lineterm=''
        )

        return ''.join(diff)

    def generate_patch_from_content(self, original_content: str, modified_content: str,
                                   file_path: str) -> str:
        """
        从内容字符串生成patch
        
        Args:
            original_content: 原始内容
            modified_content: 修改后的内容
            file_path: 文件路径
            
        Returns:
            生成的patch内容
        """
        original_lines = original_content.splitlines(keepends=True)
        modified_lines = modified_content.splitlines(keepends=True)

        diff = difflib.unified_diff(
            original_lines,
            modified_lines,
            fromfile=f"a/{file_path}",
            tofile=f"b/{file_path}",
            lineterm=''
        )

        return ''.join(diff)

    def preview_patch(self, patch_content: str) -> str:
        """
        预览patch内容，格式化显示
        
        Args:
            patch_content: patch内容
            
        Returns:
            格式化的预览内容
        """
        lines = patch_content.split('\n')
        preview_lines = []
        
        for line in lines:
            if line.startswith('@@'):
                preview_lines.append(f"\033[36m{line}\033[0m")  # 青色
            elif line.startswith('+'):
                preview_lines.append(f"\033[32m{line}\033[0m")  # 绿色
            elif line.startswith('-'):
                preview_lines.append(f"\033[31m{line}\033[0m")  # 红色
            elif line.startswith('---') or line.startswith('+++'):
                preview_lines.append(f"\033[33m{line}\033[0m")  # 黄色
            else:
                preview_lines.append(line)
        
        return '\n'.join(preview_lines)

    def create_yaml_template(self, file_path: str, patch_content: str, 
                           description: str = "") -> str:
        """
        创建YAML配置模板
        
        Args:
            file_path: 文件路径
            patch_content: patch内容
            description: 描述
            
        Returns:
            YAML配置内容
        """
        # 缩进patch内容
        indented_patch = '\n'.join('        ' + line for line in patch_content.split('\n'))
        
        yaml_template = f"""  file_patches:
    - file_path: "{file_path}"
      description: "{description}"
      backup: true
      patch_content: |
{indented_patch}"""
        
        return yaml_template


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='生成patch内容的工具')
    parser.add_argument('command', choices=['generate', 'preview', 'template'], 
                       help='操作命令')
    parser.add_argument('--original', '-o', type=str, 
                       help='原始文件路径')
    parser.add_argument('--modified', '-m', type=str,
                       help='修改后的文件路径')
    parser.add_argument('--patch-file', '-p', type=str,
                       help='patch文件路径')
    parser.add_argument('--relative-path', '-r', type=str,
                       help='在patch中显示的相对路径')
    parser.add_argument('--description', '-d', type=str, default="",
                       help='patch描述')
    parser.add_argument('--output', type=str,
                       help='输出文件路径')

    args = parser.parse_args()
    generator = PatchGenerator()

    try:
        if args.command == 'generate':
            if not args.original or not args.modified:
                print("错误: generate命令需要--original和--modified参数")
                sys.exit(1)
            
            original_file = Path(args.original)
            modified_file = Path(args.modified)
            
            patch_content = generator.generate_patch_from_files(
                original_file, modified_file, args.relative_path
            )
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(patch_content)
                print(f"Patch已保存到: {args.output}")
            else:
                print("生成的patch内容:")
                print("=" * 50)
                print(patch_content)

        elif args.command == 'preview':
            if not args.patch_file:
                print("错误: preview命令需要--patch-file参数")
                sys.exit(1)
            
            with open(args.patch_file, 'r', encoding='utf-8') as f:
                patch_content = f.read()
            
            preview = generator.preview_patch(patch_content)
            print("Patch预览:")
            print("=" * 50)
            print(preview)

        elif args.command == 'template':
            if not args.patch_file or not args.relative_path:
                print("错误: template命令需要--patch-file和--relative-path参数")
                sys.exit(1)
            
            with open(args.patch_file, 'r', encoding='utf-8') as f:
                patch_content = f.read()
            
            yaml_template = generator.create_yaml_template(
                args.relative_path, patch_content, args.description
            )
            
            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(yaml_template)
                print(f"YAML模板已保存到: {args.output}")
            else:
                print("生成的YAML配置模板:")
                print("=" * 50)
                print(yaml_template)

    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
