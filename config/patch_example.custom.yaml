# 自定义代码配置文件 - Patch示例
# 展示新的patch功能，支持对任意文件进行patch操作

version: "1.0"
product_name: "patch_example"

code_modifications:
  # 传统的函数替换方式（保持兼容）
  function_replacements:
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_preview_node.cpp"
      function_signature: "RetCode MyPreviewNode::Stop(const int32_t streamId)"
      description: "完整替换预览节点停止函数"
      replacement_code: |
        RetCode MyPreviewNode::Stop(const int32_t streamId)
        {
            CAMERA_LOGI("Custom Stop implementation for stream %d", streamId);
            // 完整的自定义实现
            return RC_OK;
        }

  # 新的patch方式 - 支持对任意文件进行增量修改
  file_patches:
    # 示例1: 修改BUILD.gn文件，添加新的依赖
    - file_path: "output/5.0.0/rk3568/board/hihope/rk3568/BUILD.gn"
      description: "在BUILD.gn中添加自定义依赖和配置"
      backup: false
      patch_content: |
        @@ -15,6 +15,10 @@
           deps = [
             "//drivers/peripheral/audio/chipsets/tfa9879:hdf_audio_tfa9879",
             "//drivers/peripheral/camera/hal/adapter:camera_adapter",
        +    "//drivers/peripheral/custom/my_driver:hdf_custom_driver",
        +    "//third_party/custom_lib:custom_lib",
           ]
        +  
        +  # 自定义配置
        +  defines = [ "ENABLE_CUSTOM_FEATURES" ]
         }

    # 示例2: 修改C++源文件，只修改特定行
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.cpp"
      description: "在捕获节点中添加自定义日志和错误处理"
      backup: false
      patch_content: |
        @@ -45,7 +45,10 @@
         RetCode MyCaptureNode::Start(const int32_t streamId)
         {
             CAMERA_LOGI("MyCaptureNode::Start streamId = %d", streamId);
        +    
        +    // 添加自定义启动逻辑
        +    CAMERA_LOGI("Custom capture node initialization");
        +    
             if (streamId < 0) {
                 CAMERA_LOGE("Invalid stream ID: %d", streamId);
                 return RC_ERROR;

    # 示例3: 修改头文件，添加新的声明
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/src/node/my_capture_node.h"
      description: "在头文件中添加自定义方法声明"
      backup: false
      patch_content: |
        @@ -25,6 +25,9 @@
         public:
             MyCaptureNode(const std::string& name, const std::string& type);
             ~MyCaptureNode() override;
        +    
        +    // 自定义方法声明
        +    RetCode CustomProcessBuffer(std::shared_ptr<IBuffer>& buffer);
         
         private:
             void OnMetadataResult(const int32_t streamId, const std::shared_ptr<CameraMetadata>& result) override;

    # 示例4: 修改配置文件，添加新的配置项
    - file_path: "output/5.0.0/rk3568/vendor/hihope/rk3568/config.json"
      description: "在产品配置中添加自定义配置项"
      backup: false
      patch_content: |
        @@ -45,6 +45,12 @@
             "camera_config": {
               "preview_resolution": "1920x1080",
               "capture_resolution": "3264x2448"
        +    },
        +    "custom_config": {
        +      "enable_advanced_features": true,
        +      "custom_driver_path": "/vendor/lib64/hw/custom_driver.so",
        +      "debug_level": 2,
        +      "performance_mode": "high"
             }
           }
         }

    # 示例5: 修改Makefile或其他构建文件
    - file_path: "output/5.0.0/rk3568/board/camera/vdi_impl/v4l2/pipeline_core/BUILD.gn"
      description: "在相机pipeline构建文件中添加自定义编译选项"
      backup: false
      patch_content: |
        @@ -12,6 +12,9 @@
         
         config("pipeline_core_config") {
           visibility = [ ":*" ]
        +  
        +  # 自定义编译选项
        +  cflags = [ "-DENABLE_CUSTOM_PIPELINE" ]
         }
         
         ohos_shared_library("camera_pipeline_core") {
        @@ -28,6 +31,7 @@
             "//drivers/peripheral/camera/hal/utils:camera_utils",
             "//third_party/libjpeg-turbo:turbojpeg_static",
           ]
        +  deps += [ "//vendor/custom/camera:custom_pipeline_ext" ]
         }

    # 示例6: 修改Shell脚本或其他文本文件
    - file_path: "output/5.0.0/rk3568/board/hihope/rk3568/init.rk3568.rc"
      description: "在init脚本中添加自定义服务启动"
      backup: false
      patch_content: |
        @@ -15,6 +15,12 @@
         on boot
             # 启动相机服务
             start camera_service
        +    
        +    # 启动自定义服务
        +    start custom_driver_service
        +    
        +service custom_driver_service /vendor/bin/custom_driver
        +    class main
        +    user system
        +    group system
         
         on property:sys.boot_completed=1

# 使用说明:
# 1. patch_content使用标准的unified diff格式
# 2. @@ -old_start,old_count +new_start,new_count @@ 表示修改的行范围
# 3. 以空格开头的行表示上下文（不变的行）
# 4. 以-开头的行表示要删除的行
# 5. 以+开头的行表示要添加的行
# 6. backup: false 表示不创建备份文件（默认值），设为true时会创建备份
# 7. 支持对任意文本文件进行patch操作，不仅限于C++代码

# 优势:
# - 只修改需要改变的部分，配置更简洁
# - 可以适应模板代码的小幅变化
# - 支持对BUILD.gn、JSON、脚本等各种文件类型进行修改
# - 标准的diff格式，可以使用标准工具生成和验证
