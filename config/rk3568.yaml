ohos_version: 5.0.0
ohos_path: /home/<USER>/ohos_5.0/openharmony-5.0.0
product_name: rk3568  # dayu210
device_company: hihope   # hihope orangepi
target_cpu: arm64
soc: rk3568
soc_company: rockchip
board_code:
  - kernel:
      linux_kernel_version: 5.10
      #指定linux内核源码路径,采用ohos源码下的 相对路径
      linux_kernel_source_path: "kernel/linux/${linux_kernel_version}"
      if_use_specific_kernel: false
      if_use_kernel_patch: true
      # kenel patch统一存放在device/board/xxx/xxx/kernel/kernel_patch/5.10目录下
      patch_path:
        - kernel_patch: kernel/linux/patches/linux-5.10/rk3568_patch/kernel.patch
        - hdf_patch: kernel/linux/patches/linux-5.10/rk3568_patch/hdf.patch
  - audio_drivers:
      enabled: true
      adm_drivers:
        enable: true
        codec_chip: rk809
        compatible_driver: rk817
        bus_type: platform # or i2c
      alsa_drivers:
        enable: false
        codec_chip: es8323
        implementation_method: framework # framework  manual
  - camera_drivers:
      enabled: true
      method: v4l2 # v4l2 or usb
      sensor: rkispv5
      node:
        - exif
        - codec
  - cfg:
      enabled: true
  - distributedhardware:
      enabled: true
  - uboot:
      enabled: false
  - build_config:
      enabled: true
      build_gn: true
      device_gni: true
      ohos_build: true
  - loader:
      enabled: true
      description: "Loader files (no BUILD.gn needed)"
  - resources:
      enabled: false
      description: "Resource files for the board"
  - updater:
      enabled: true
      description: "Updater configuration files"
  - hardware:
      enabled: false
      description: "Custom Hardware abstraction layer"
  - bootanimation:
      enabled: true
      description: "Boot animation files and configuration"
# SoC components configuration
soc_code:
  - display:
      enabled: true
      description: "Display controller for RK3568"
  - gpu:
      enabled: true
      description: "GPU support for RK3568"
  - codec:
      enabled: true
      description: "Hardware codec support"
  - omx_il:
      enabled: true
      description: "OpenMAX IL components"
  - mpp:
      enabled: true
      description: "Media Processing Platform"
  - rga:
      enabled: true
      description: "Rockchip Graphics Accelerator"
  - build_config:
      enabled: true
      description: "SoC build configuration"
  - my_group:
      enabled: true
      description: "Custom SoC component"
# Vendor components configuration
vendor_code:
  - bluetooth:
      enabled: true
      description: "Bluetooth configuration for vendor"
  - resourceschedule:
      enabled: true
      description: "Resource schedule configuration"
  - security_config:
      enabled: true
      description: "Security configuration"
  - hdf_config:
      enabled: true
      description: "HDF configuration"
  - default_app_config:
      enabled: true
      description: "Default app configuration"
  - build_config:
      enabled: true
      description: "Vendor build configuration"
  - etc:
      enabled: true
  - hals:
      enabled: true
  - image_conf:
      enabled: true
  - preinstall-config:
      enabled: true
  - updater_config:
      enabled: true
  - window_config:
      enabled: true