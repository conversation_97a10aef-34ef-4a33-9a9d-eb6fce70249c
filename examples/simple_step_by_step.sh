#!/bin/bash
#
# 简洁的分步骤适配脚本
#
# 这个脚本用最简单的方式展示分步骤适配：
# 1. 生成基础代码
# 2. 应用MPP模块patch → 复制MPP相关文件
# 3. 应用Camera模块patch → 复制Camera相关文件
# 4. 应用Audio模块patch → 复制Audio相关文件
# 5. 应用Vendor配置patch → 复制配置文件
#

set -e  # 遇到错误立即退出

# 项目配置
PRODUCT_NAME="rk3568"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TARGET_DIR="$PROJECT_ROOT/target"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# 运行命令函数
run_command() {
    local cmd="$1"
    local description="$2"
    
    echo -e "\n${BLUE}🔧 $description${NC}"
    echo "命令: $cmd"
    
    cd "$PROJECT_ROOT"
    if eval "$cmd"; then
        echo -e "${GREEN}✅ $description - 成功${NC}"
        return 0
    else
        echo -e "${RED}❌ $description - 失败${NC}"
        return 1
    fi
}

# 复制文件函数
copy_files() {
    local source_pattern="$1"
    local target_subdir="$2"
    local description="$3"
    
    echo -e "\n${BLUE}📁 $description${NC}"
    
    # 创建目标目录
    local target_path="$TARGET_DIR/$target_subdir"
    mkdir -p "$target_path"
    
    # 复制文件
    local copied_count=0
    
    # 使用find查找文件并复制
    if find "$PROJECT_ROOT" -path "$source_pattern" -type f 2>/dev/null | while read -r file; do
        if [ -f "$file" ]; then
            cp "$file" "$target_path/"
            echo "   📄 $(basename "$file") → $target_path/"
            copied_count=$((copied_count + 1))
        fi
    done; then
        echo -e "${GREEN}✅ 复制完成${NC}"
    else
        echo -e "${YELLOW}⚠️  没有找到匹配的文件: $source_pattern${NC}"
    fi
}

# 复制目录函数
copy_directory() {
    local source_dir="$1"
    local target_subdir="$2"
    local description="$3"
    
    echo -e "\n${BLUE}📁 $description${NC}"
    
    if [ -d "$source_dir" ]; then
        local target_path="$TARGET_DIR/$target_subdir"
        mkdir -p "$target_path"
        
        # 复制整个目录
        cp -r "$source_dir"/* "$target_path/" 2>/dev/null || true
        
        local file_count=$(find "$target_path" -type f | wc -l)
        echo "   📁 $(basename "$source_dir")/ → $target_path/ ($file_count 个文件)"
        echo -e "${GREEN}✅ 复制完成，共 $file_count 个文件${NC}"
    else
        echo -e "${YELLOW}⚠️  源目录不存在: $source_dir${NC}"
    fi
}

# 主函数
main() {
    echo "🚀 RK3568 简洁分步骤适配"
    echo "=================================================="
    
    # 创建目标目录
    mkdir -p "$TARGET_DIR"
    
    # 步骤1: 生成基础代码
    echo -e "\n${BLUE}📝 步骤1: 生成基础代码${NC}"
    if ! run_command "python -m src.oh_codegen.simple_cli $PRODUCT_NAME.yaml --clean" "生成RK3568基础代码"; then
        return 1
    fi
    
    # 步骤2: MPP模块
    echo -e "\n${BLUE}🎬 步骤2: 处理MPP模块${NC}"
    
    # 2.1 应用MPP patch
    local mpp_patch="patches/$PRODUCT_NAME/modules/soc/mpp.yaml"
    if [ -f "$mpp_patch" ]; then
        if ! run_command "python src/oh_codegen/tools/patch_apply_cli.py apply-yaml $mpp_patch" "应用MPP模块patch"; then
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  MPP patch文件不存在: $mpp_patch${NC}"
    fi
    
    # 2.2 复制MPP文件
    copy_directory "output/5.0.0/$PRODUCT_NAME/soc/$PRODUCT_NAME/mpp" "mpp" "复制MPP相关文件"
    
    # 步骤3: Camera模块
    echo -e "\n${BLUE}📷 步骤3: 处理Camera模块${NC}"
    
    # 3.1 应用Camera patch
    local camera_patch="patches/$PRODUCT_NAME/modules/board/camera.yaml"
    if [ -f "$camera_patch" ]; then
        if ! run_command "python src/oh_codegen/tools/patch_apply_cli.py apply-yaml $camera_patch" "应用Camera模块patch"; then
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Camera patch文件不存在: $camera_patch${NC}"
    fi
    
    # 3.2 复制Camera文件
    copy_directory "output/5.0.0/$PRODUCT_NAME/board/camera" "camera" "复制Camera相关文件"
    
    # 步骤4: Audio模块
    echo -e "\n${BLUE}🔊 步骤4: 处理Audio模块${NC}"
    
    # 4.1 应用Audio patch
    local audio_patch="patches/$PRODUCT_NAME/modules/board/audio.yaml"
    if [ -f "$audio_patch" ]; then
        if ! run_command "python src/oh_codegen/tools/patch_apply_cli.py apply-yaml $audio_patch" "应用Audio模块patch"; then
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Audio patch文件不存在: $audio_patch${NC}"
    fi
    
    # 4.2 复制Audio文件
    copy_directory "output/5.0.0/$PRODUCT_NAME/board/audio" "audio" "复制Audio相关文件"
    
    # 步骤5: Vendor配置
    echo -e "\n${BLUE}⚙️  步骤5: 处理Vendor配置${NC}"
    
    # 5.1 应用Vendor patch
    local vendor_patch="patches/$PRODUCT_NAME/modules/vendor/product.yaml"
    if [ -f "$vendor_patch" ]; then
        if ! run_command "python src/oh_codegen/tools/patch_apply_cli.py apply-yaml $vendor_patch" "应用Vendor配置patch"; then
            return 1
        fi
    else
        echo -e "${YELLOW}⚠️  Vendor patch文件不存在: $vendor_patch${NC}"
    fi
    
    # 5.2 复制Vendor文件
    copy_directory "output/5.0.0/$PRODUCT_NAME/vendor" "vendor" "复制Vendor相关文件"
    
    # 步骤6: 显示结果
    echo -e "\n${BLUE}📊 步骤6: 适配结果${NC}"
    echo "------------------------------"
    
    local total_files=0
    for dir in "$TARGET_DIR"/*; do
        if [ -d "$dir" ]; then
            local dir_name=$(basename "$dir")
            local file_count=$(find "$dir" -type f | wc -l)
            total_files=$((total_files + file_count))
            echo "📁 $dir_name: $file_count 个文件"
        fi
    done
    
    echo "📊 总计: $total_files 个文件"
    echo "📁 结果目录: $TARGET_DIR"
    
    # 生成简单的报告
    cat > "$TARGET_DIR/README.txt" << EOF
RK3568适配结果
================

适配时间: $(date)
总文件数: $total_files

目录说明:
- mpp/: 媒体处理平台相关文件
- camera/: 相机驱动相关文件
- audio/: 音频驱动相关文件
- vendor/: 产品配置相关文件

使用方法:
1. 查看各目录下的文件
2. 根据需要集成到您的项目中
3. 参考BUILD.gn文件了解构建配置
EOF
    
    echo -e "\n${GREEN}🎉 适配完成！${NC}"
    echo "📋 查看详细报告: $TARGET_DIR/README.txt"
    
    return 0
}

# 显示帮助
show_help() {
    cat << EOF
RK3568简洁分步骤适配脚本

这个脚本演示如何分步骤进行开发板适配：
1. 生成基础代码
2. 逐个模块应用patch并复制文件
3. 生成适配结果报告

用法: $0 [选项]

选项:
  --help, -h    显示此帮助信息

示例:
  $0            # 执行完整的分步骤适配

输出:
  target/       # 适配结果目录
  ├── mpp/      # MPP模块文件
  ├── camera/   # Camera模块文件
  ├── audio/    # Audio模块文件
  ├── vendor/   # Vendor配置文件
  └── README.txt # 适配报告
EOF
}

# 解析命令行参数
case "${1:-}" in
    --help|-h)
        show_help
        exit 0
        ;;
    "")
        # 无参数，执行主函数
        ;;
    *)
        echo "未知参数: $1"
        show_help
        exit 1
        ;;
esac

# 执行主函数
if main; then
    echo -e "\n${GREEN}✅ 所有步骤执行成功${NC}"
    exit 0
else
    echo -e "\n${RED}❌ 执行过程中出现错误${NC}"
    exit 1
fi
