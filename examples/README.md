# 开发板适配示例集合

这个目录包含了完整的开发板适配示例，展示了多种不同的适配方法和工具。

## 📚 示例列表

### 🚀 一体化适配示例

#### [rk3568_adaptation_demo.sh](rk3568_adaptation_demo.sh)
- **类型**: Bash脚本
- **特点**: 一次性应用所有patch，适合快速适配
- **用法**: `./examples/rk3568_adaptation_demo.sh --preview`

#### [rk3568_adaptation_guide.md](rk3568_adaptation_guide.md)
- **类型**: 详细指南
- **特点**: 完整的RK3568适配流程说明
- **内容**: 从代码生成到patch应用的完整步骤

### 🔧 分步骤适配示例（新功能）

#### [simple_step_by_step.py](simple_step_by_step.py) ⭐ 推荐
- **类型**: Python脚本（简洁版）
- **特点**: 最简单直观的分步骤适配，一看就懂
- **优势**: 清晰展示每一步在做什么，适合学习和演示
- **用法**: `python examples/simple_step_by_step.py`

#### [simple_step_by_step.sh](simple_step_by_step.sh) ⭐ 推荐
- **类型**: Bash脚本（简洁版）
- **特点**: 纯Shell实现的简洁分步骤适配
- **优势**: 无需Python依赖，步骤清晰明了
- **用法**: `./examples/simple_step_by_step.sh`

#### [SIMPLE_GUIDE.md](SIMPLE_GUIDE.md)
- **类型**: 简洁指南
- **特点**: 一看就懂的使用说明
- **内容**: 每一步的作用和输出结果

#### [step_by_step_adaptation.py](step_by_step_adaptation.py)
- **类型**: Python脚本
- **特点**: 分模块逐步适配，支持交互式和批处理模式
- **优势**: 清晰可控，便于调试和学习
- **用法**: 
  ```bash
  # 交互式模式
  python examples/step_by_step_adaptation.py
  
  # 批处理模式
  python examples/step_by_step_adaptation.py --batch
  
  # 只处理特定模块
  python examples/step_by_step_adaptation.py --batch --modules camera audio
  ```

#### [step_by_step_adaptation.sh](step_by_step_adaptation.sh)
- **类型**: Bash脚本
- **特点**: 分模块逐步适配的Bash版本
- **优势**: 无需Python依赖，纯Shell实现
- **用法**:
  ```bash
  # 交互式模式
  ./examples/step_by_step_adaptation.sh
  
  # 批处理模式
  ./examples/step_by_step_adaptation.sh --batch
  
  # 只处理特定模块
  ./examples/step_by_step_adaptation.sh --batch camera audio
  ```

#### [STEP_BY_STEP_ADAPTATION_GUIDE.md](STEP_BY_STEP_ADAPTATION_GUIDE.md)
- **类型**: 详细指南
- **特点**: 分步骤适配的完整使用指南
- **内容**: 包含使用示例、配置说明、故障排除等

### 📋 设计文档

#### [step_by_step_adaptation_design.md](step_by_step_adaptation_design.md)
- **类型**: 设计文档
- **内容**: 分步骤适配流程的设计思路和架构说明

## 🎯 选择适合的示例

### 快速适配 → 使用一体化示例
如果您想快速完成适配，推荐使用：
```bash
./examples/rk3568_adaptation_demo.sh --preview  # 预览
./examples/rk3568_adaptation_demo.sh            # 执行
```

### 学习理解 → 使用简洁分步骤示例 ⭐
如果您想清楚地看到每一步在做什么，推荐使用：
```bash
python examples/simple_step_by_step.py         # 简洁直观
./examples/simple_step_by_step.sh              # Bash版本
```

### 高级功能 → 使用完整分步骤示例
如果您需要交互式控制和高级功能，推荐使用：
```bash
python examples/step_by_step_adaptation.py     # 交互式学习
```

### 调试问题 → 使用分步骤示例
如果适配过程中遇到问题，推荐使用：
```bash
# 只处理有问题的模块
python examples/step_by_step_adaptation.py --batch --modules camera
```

### 团队协作 → 使用分步骤示例
如果多人协作开发，推荐使用：
```bash
# 不同人员处理不同模块
./examples/step_by_step_adaptation.sh --batch camera    # 硬件工程师
./examples/step_by_step_adaptation.sh --batch mpp gpu   # 多媒体工程师
./examples/step_by_step_adaptation.sh --batch product   # 产品工程师
```

## 🔄 适配流程对比

| 特性 | 一体化适配 | 分步骤适配 |
|------|------------|------------|
| **速度** | 快速 | 较慢 |
| **可控性** | 有限 | 精确 |
| **调试友好** | 一般 | 优秀 |
| **学习价值** | 有限 | 很高 |
| **团队协作** | 一般 | 优秀 |
| **问题定位** | 困难 | 容易 |

## 📁 目录结构

```
examples/
├── README.md                              # 本文件
├── rk3568_adaptation_demo.sh              # 一体化适配脚本
├── rk3568_adaptation_guide.md             # 一体化适配指南
├── simple_step_by_step.py                 # 简洁分步骤Python脚本 ⭐
├── simple_step_by_step.sh                 # 简洁分步骤Bash脚本 ⭐
├── SIMPLE_GUIDE.md                        # 简洁分步骤指南 ⭐
├── step_by_step_adaptation.py             # 完整分步骤Python脚本
├── step_by_step_adaptation.sh             # 完整分步骤Bash脚本
├── STEP_BY_STEP_ADAPTATION_GUIDE.md       # 完整分步骤指南
└── step_by_step_adaptation_design.md      # 分步骤适配设计文档
```

## 🚀 快速开始

### 1. 选择适配方式

```bash
# 方式1: 一体化适配（推荐新手）
./examples/rk3568_adaptation_demo.sh --preview

# 方式2: 简洁分步骤适配（推荐学习） ⭐
python examples/simple_step_by_step.py

# 方式3: 完整分步骤适配（推荐进阶用户）
python examples/step_by_step_adaptation.py
```

### 2. 查看结果

```bash
# 查看适配结果
tree target/

# 查看适配报告
cat target/adaptation_report.json  # 或 .txt
```

### 3. 验证适配

```bash
# 检查关键文件
ls -la output/5.0.0/rk3568/

# 验证patch应用结果
grep -r "Custom.*implementation" output/5.0.0/rk3568/
```

## 🛠️ 自定义适配

### 创建您的产品适配

1. **复制模块配置**:
   ```bash
   cp -r patches/rk3568/modules patches/your_product/
   ```

2. **修改配置文件**:
   ```bash
   # 更新产品名称和文件路径
   sed -i 's/rk3568/your_product/g' patches/your_product/modules/*/*.yaml
   ```

3. **运行适配**:
   ```bash
   python examples/step_by_step_adaptation.py --product your_product
   ```

### 添加新模块

1. **创建模块配置**:
   ```bash
   cp patches/rk3568/modules/board/camera.yaml patches/rk3568/modules/board/wifi.yaml
   ```

2. **修改脚本**:
   ```python
   # 在step_by_step_adaptation.py中添加
   self.module_order = [
       # ... 现有模块
       ("board", "wifi", "WiFi驱动模块"),
   ]
   ```

## 📖 相关文档

- [Patch应用API和CLI指南](../docs/PATCH_API_AND_CLI_GUIDE.md)
- [Patch目录结构化指南](../docs/PATCH_DIRECTORY_STRUCTURE_GUIDE.md)
- [自定义代码指南](../docs/CUSTOM_CODE_GUIDE.md)

## 🤝 贡献

欢迎贡献新的适配示例！请确保：

1. 提供完整的使用说明
2. 包含错误处理和验证
3. 添加详细的注释
4. 测试在不同环境下的兼容性

---

🎉 **选择适合您的适配方式，享受高效的开发板适配体验！**
