#!/usr/bin/env python3
"""
简洁的分步骤适配脚本

这个脚本用最简单的方式展示分步骤适配：
1. 生成基础代码
2. 应用MPP模块patch → 复制MPP相关文件
3. 应用Camera模块patch → 复制Camera相关文件
4. 应用Audio模块patch → 复制Audio相关文件
5. 应用Vendor配置patch → 复制配置文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 项目配置
PRODUCT_NAME = "rk3568"
PROJECT_ROOT = Path(__file__).parent.parent
TARGET_DIR = PROJECT_ROOT / "target"

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔧 {description}")
    print(f"命令: {cmd}")
    
    result = subprocess.run(cmd, shell=True, cwd=PROJECT_ROOT, 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        print(f"✅ {description} - 成功")
        return True
    else:
        print(f"❌ {description} - 失败")
        print(f"错误: {result.stderr}")
        return False

def copy_files(source_pattern, target_dir, description):
    """复制文件到目标目录"""
    print(f"\n📁 {description}")
    
    # 创建目标目录
    target_path = TARGET_DIR / target_dir
    target_path.mkdir(parents=True, exist_ok=True)
    
    # 查找并复制文件
    source_files = list(PROJECT_ROOT.glob(source_pattern))
    copied_count = 0
    
    for source_file in source_files:
        if source_file.is_file():
            target_file = target_path / source_file.name
            shutil.copy2(source_file, target_file)
            copied_count += 1
            print(f"   📄 {source_file.name} → {target_file}")
        elif source_file.is_dir():
            target_subdir = target_path / source_file.name
            if target_subdir.exists():
                shutil.rmtree(target_subdir)
            shutil.copytree(source_file, target_subdir)
            file_count = len(list(target_subdir.rglob('*')))
            copied_count += file_count
            print(f"   📁 {source_file.name}/ → {target_subdir}/ ({file_count} 个文件)")
    
    print(f"✅ 复制完成，共 {copied_count} 个文件")
    return copied_count > 0

def main():
    """主函数 - 分步骤执行适配"""
    print("🚀 RK3568 简洁分步骤适配")
    print("=" * 50)
    
    # 创建目标目录
    TARGET_DIR.mkdir(exist_ok=True)
    
    # 步骤1: 生成基础代码
    print("\n📝 步骤1: 生成基础代码")
    if not run_command(f"python -m src.oh_codegen.simple_cli {PRODUCT_NAME}.yaml --clean", 
                      "生成RK3568基础代码"):
        return False
    
    # 步骤2: MPP模块
    print("\n🎬 步骤2: 处理MPP模块")
    
    # 2.1 应用MPP patch
    mpp_patch = f"patches/{PRODUCT_NAME}/modules/soc/mpp.yaml"
    if Path(mpp_patch).exists():
        if not run_command(f"python src/oh_codegen/tools/patch_apply_cli.py apply-yaml {mpp_patch}", 
                          "应用MPP模块patch"):
            return False
    else:
        print(f"⚠️  MPP patch文件不存在: {mpp_patch}")
    
    # 2.2 复制MPP文件
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/soc/{PRODUCT_NAME}/mpp/*", 
               "mpp", "复制MPP相关文件")
    
    # 步骤3: Camera模块
    print("\n📷 步骤3: 处理Camera模块")
    
    # 3.1 应用Camera patch
    camera_patch = f"patches/{PRODUCT_NAME}/modules/board/camera.yaml"
    if Path(camera_patch).exists():
        if not run_command(f"python src/oh_codegen/tools/patch_apply_cli.py apply-yaml {camera_patch}", 
                          "应用Camera模块patch"):
            return False
    else:
        print(f"⚠️  Camera patch文件不存在: {camera_patch}")
    
    # 3.2 复制Camera文件
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/board/camera/**/*.cpp", 
               "camera/src", "复制Camera源文件")
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/board/camera/**/*.h", 
               "camera/include", "复制Camera头文件")
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/board/camera/**/BUILD.gn", 
               "camera/build", "复制Camera构建文件")
    
    # 步骤4: Audio模块
    print("\n🔊 步骤4: 处理Audio模块")
    
    # 4.1 应用Audio patch
    audio_patch = f"patches/{PRODUCT_NAME}/modules/board/audio.yaml"
    if Path(audio_patch).exists():
        if not run_command(f"python src/oh_codegen/tools/patch_apply_cli.py apply-yaml {audio_patch}", 
                          "应用Audio模块patch"):
            return False
    else:
        print(f"⚠️  Audio patch文件不存在: {audio_patch}")
    
    # 4.2 复制Audio文件
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/board/audio/**/*", 
               "audio", "复制Audio相关文件")
    
    # 步骤5: Vendor配置
    print("\n⚙️  步骤5: 处理Vendor配置")
    
    # 5.1 应用Vendor patch
    vendor_patch = f"patches/{PRODUCT_NAME}/modules/vendor/product.yaml"
    if Path(vendor_patch).exists():
        if not run_command(f"python src/oh_codegen/tools/patch_apply_cli.py apply-yaml {vendor_patch}", 
                          "应用Vendor配置patch"):
            return False
    else:
        print(f"⚠️  Vendor patch文件不存在: {vendor_patch}")
    
    # 5.2 复制Vendor文件
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/vendor/**/*.json", 
               "vendor/config", "复制Vendor配置文件")
    copy_files(f"output/5.0.0/{PRODUCT_NAME}/vendor/**/*.cpp", 
               "vendor/src", "复制Vendor源文件")
    
    # 步骤6: 显示结果
    print("\n📊 步骤6: 适配结果")
    print("-" * 30)
    
    total_files = 0
    for item in TARGET_DIR.iterdir():
        if item.is_dir():
            file_count = len(list(item.rglob('*')))
            total_files += file_count
            print(f"📁 {item.name}: {file_count} 个文件")
    
    print(f"📊 总计: {total_files} 个文件")
    print(f"📁 结果目录: {TARGET_DIR}")
    
    # 生成简单的报告
    with open(TARGET_DIR / "README.txt", "w", encoding="utf-8") as f:
        f.write(f"RK3568适配结果\n")
        f.write(f"================\n\n")
        f.write(f"适配时间: {os.popen('date').read().strip()}\n")
        f.write(f"总文件数: {total_files}\n\n")
        f.write(f"目录说明:\n")
        f.write(f"- mpp/: 媒体处理平台相关文件\n")
        f.write(f"- camera/: 相机驱动相关文件\n")
        f.write(f"- audio/: 音频驱动相关文件\n")
        f.write(f"- vendor/: 产品配置相关文件\n")
    
    print("\n🎉 适配完成！")
    print(f"📋 查看详细报告: {TARGET_DIR}/README.txt")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 所有步骤执行成功")
            sys.exit(0)
        else:
            print("\n❌ 执行过程中出现错误")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生异常: {e}")
        sys.exit(1)
