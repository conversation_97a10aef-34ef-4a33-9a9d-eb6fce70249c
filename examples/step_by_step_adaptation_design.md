# 分步骤适配流程设计

## 概述

这个示例展示如何分步骤地进行开发板适配，每次应用一个模块的patch，然后复制相关文件到指定目录。这种方式让用户可以清楚地看到每一步的效果，便于调试和理解。

## 适配流程设计

### 步骤1：生成基础代码
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml --clean
```

### 步骤2：分模块应用patch

#### 2.1 MPP模块（媒体处理平台）
- 应用patch：`patches/rk3568/soc/mpp.yaml`
- 复制文件：`output/5.0.0/rk3568/soc/rk3568/mpp/` → `target/mpp/`
- 验证：检查MPP相关的BUILD.gn和源文件

#### 2.2 Camera模块（相机驱动）
- 应用patch：`patches/rk3568/board/camera.yaml`
- 复制文件：`output/5.0.0/rk3568/board/camera/` → `target/camera/`
- 验证：检查相机驱动相关文件

#### 2.3 Audio模块（音频驱动）
- 应用patch：`patches/rk3568/board/audio.yaml`
- 复制文件：`output/5.0.0/rk3568/board/audio/` → `target/audio/`
- 验证：检查音频驱动相关文件

#### 2.4 GPU模块（图形处理）
- 应用patch：`patches/rk3568/soc/gpu.yaml`
- 复制文件：`output/5.0.0/rk3568/soc/rk3568/gpu/` → `target/gpu/`
- 验证：检查GPU相关文件

#### 2.5 Vendor配置（产品定制）
- 应用patch：`patches/rk3568/vendor/product.yaml`
- 复制文件：`output/5.0.0/rk3568/vendor/hihope/rk3568/` → `target/vendor/`
- 验证：检查产品配置文件

### 步骤3：最终验证
- 检查所有复制的文件
- 验证patch应用结果
- 生成适配报告

## 模块化patch配置结构

```
patches/rk3568/
├── board/
│   ├── camera.yaml          # 相机模块patch
│   ├── audio.yaml           # 音频模块patch
│   └── kernel.yaml          # 内核模块patch
├── soc/
│   ├── mpp.yaml             # MPP模块patch
│   ├── gpu.yaml             # GPU模块patch
│   └── display.yaml         # 显示模块patch
└── vendor/
    ├── product.yaml         # 产品配置patch
    └── security.yaml        # 安全配置patch
```

## 文件复制策略

### 复制规则
1. **选择性复制** - 只复制被patch修改过的文件
2. **目录结构保持** - 保持原有的目录层次结构
3. **备份机制** - 复制前创建备份
4. **验证机制** - 复制后验证文件完整性

### 目标目录结构
```
target/
├── mpp/                     # MPP模块文件
│   ├── BUILD.gn
│   ├── src/
│   └── include/
├── camera/                  # 相机模块文件
│   ├── BUILD.gn
│   ├── drivers/
│   └── hal/
├── audio/                   # 音频模块文件
│   ├── BUILD.gn
│   └── drivers/
├── gpu/                     # GPU模块文件
│   ├── BUILD.gn
│   └── drivers/
└── vendor/                  # 产品配置文件
    ├── config.json
    └── BUILD.gn
```

## 交互式操作

### Python版本特性
- 交互式确认每个步骤
- 实时显示文件复制进度
- 支持跳过某些模块
- 提供详细的错误信息

### Bash版本特性
- 清晰的步骤提示
- 颜色化输出
- 支持批处理模式
- 简单的错误处理

## 验证机制

### 每步验证
1. **patch应用验证** - 检查patch是否成功应用
2. **文件存在验证** - 检查目标文件是否存在
3. **内容验证** - 检查关键内容是否正确修改

### 最终验证
1. **完整性检查** - 检查所有模块文件是否完整
2. **依赖关系检查** - 检查模块间的依赖关系
3. **构建验证** - 验证生成的代码是否可以构建

## 使用场景

### 开发调试
- 逐步验证每个模块的patch效果
- 快速定位问题模块
- 便于增量开发

### 学习理解
- 清楚地看到每个模块的作用
- 理解模块间的依赖关系
- 学习OpenHarmony的架构

### 团队协作
- 不同团队负责不同模块
- 独立验证各自的patch
- 便于代码审查和测试
