# 简洁分步骤适配指南

## 🎯 一句话说明

这个脚本让您清楚地看到每一步在做什么：生成代码 → 应用patch → 复制文件 → 下一个模块。

## 🚀 快速使用

### Python版本（推荐）
```bash
python examples/simple_step_by_step.py
```

### Bash版本
```bash
./examples/simple_step_by_step.sh
```

## 📋 执行步骤

脚本会按顺序执行以下步骤：

### 步骤1: 生成基础代码
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml --clean
```
**作用**: 生成OpenHarmony的基础代码框架

### 步骤2: 处理MPP模块
```bash
# 应用patch
python src/oh_codegen/tools/patch_apply_cli.py apply-yaml patches/rk3568/modules/soc/mpp.yaml

# 复制文件
cp -r output/5.0.0/rk3568/soc/rk3568/mpp/* target/mpp/
```
**作用**: 修改媒体处理平台代码，复制相关文件

### 步骤3: 处理Camera模块
```bash
# 应用patch
python src/oh_codegen/tools/patch_apply_cli.py apply-yaml patches/rk3568/modules/board/camera.yaml

# 复制文件
cp -r output/5.0.0/rk3568/board/camera/* target/camera/
```
**作用**: 修改相机驱动代码，复制相关文件

### 步骤4: 处理Audio模块
```bash
# 应用patch
python src/oh_codegen/tools/patch_apply_cli.py apply-yaml patches/rk3568/modules/board/audio.yaml

# 复制文件
cp -r output/5.0.0/rk3568/board/audio/* target/audio/
```
**作用**: 修改音频驱动代码，复制相关文件

### 步骤5: 处理Vendor配置
```bash
# 应用patch
python src/oh_codegen/tools/patch_apply_cli.py apply-yaml patches/rk3568/modules/vendor/product.yaml

# 复制文件
cp -r output/5.0.0/rk3568/vendor/* target/vendor/
```
**作用**: 修改产品配置，复制配置文件

## 📊 执行结果

运行完成后，您会看到：

```
🚀 RK3568 简洁分步骤适配
==================================================

📝 步骤1: 生成基础代码
🔧 生成RK3568基础代码
✅ 生成RK3568基础代码 - 成功

🎬 步骤2: 处理MPP模块
🔧 应用MPP模块patch
✅ 应用MPP模块patch - 成功
📁 复制MPP相关文件
✅ 复制完成，共 15 个文件

📷 步骤3: 处理Camera模块
🔧 应用Camera模块patch
✅ 应用Camera模块patch - 成功
📁 复制Camera相关文件
✅ 复制完成，共 23 个文件

🔊 步骤4: 处理Audio模块
🔧 应用Audio模块patch
✅ 应用Audio模块patch - 成功
📁 复制Audio相关文件
✅ 复制完成，共 12 个文件

⚙️  步骤5: 处理Vendor配置
🔧 应用Vendor配置patch
✅ 应用Vendor配置patch - 成功
📁 复制Vendor相关文件
✅ 复制完成，共 8 个文件

📊 步骤6: 适配结果
------------------------------
📁 mpp: 15 个文件
📁 camera: 23 个文件
📁 audio: 12 个文件
📁 vendor: 8 个文件
📊 总计: 58 个文件
📁 结果目录: target/

🎉 适配完成！
📋 查看详细报告: target/README.txt
```

## 📁 输出目录结构

```
target/
├── mpp/                    # MPP模块文件
│   ├── BUILD.gn
│   ├── mpp_manager.cpp
│   └── mpp_manager.h
├── camera/                 # Camera模块文件
│   ├── BUILD.gn
│   ├── src/
│   └── include/
├── audio/                  # Audio模块文件
│   ├── BUILD.gn
│   └── drivers/
├── vendor/                 # Vendor配置文件
│   ├── BUILD.gn
│   ├── config.json
│   └── product_config.cpp
└── README.txt              # 适配报告
```

## 🔍 每一步的作用

| 步骤 | 模块 | 作用 | 输出 |
|------|------|------|------|
| 1 | 基础代码 | 生成OpenHarmony框架 | output/5.0.0/rk3568/ |
| 2 | MPP | 修改媒体处理代码 | target/mpp/ |
| 3 | Camera | 修改相机驱动代码 | target/camera/ |
| 4 | Audio | 修改音频驱动代码 | target/audio/ |
| 5 | Vendor | 修改产品配置 | target/vendor/ |

## 🛠️ 自定义使用

### 修改产品名称
```python
# 在脚本开头修改
PRODUCT_NAME = "your_product"
```

### 添加新模块
```python
# 在main()函数中添加新步骤
print("\n🔌 步骤X: 处理WiFi模块")
run_command("python src/oh_codegen/tools/patch_apply_cli.py apply-yaml patches/rk3568/modules/board/wifi.yaml", 
           "应用WiFi模块patch")
copy_files("output/5.0.0/rk3568/board/wifi/*", "wifi", "复制WiFi相关文件")
```

### 跳过某个模块
```python
# 注释掉不需要的步骤
# print("\n🔊 步骤4: 处理Audio模块")
# ...
```

## ❓ 常见问题

**Q: 为什么要分步骤？**
A: 让您清楚地看到每一步在做什么，出错时容易定位问题。

**Q: 可以只运行某一步吗？**
A: 可以，注释掉不需要的步骤即可。

**Q: 如何验证结果？**
A: 查看target/目录下的文件，检查是否包含您期望的修改。

**Q: 出错了怎么办？**
A: 脚本会显示具体的错误信息，根据提示检查配置文件和路径。

---

🎉 **就是这么简单！每一步都清清楚楚，一目了然！**
