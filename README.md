# OpenHarmony device 代码生成工具

[English](README_en.md) | 中文

OpenHarmony device 代码生成工具能够自动生成设备特定的代码结构、构建配置和驱动实现。

## 项目简介

在 OpenHarmony 生态中，目前 device 模块已实现 board 与 soc 的解耦设计，这一架构优化虽带来了一定灵活性，但随之也衍生出新的问题 —— 不同 board 与 soc 组合下，存在大量重复性代码。这些冗余代码不仅增加了开发维护成本，还可能导致潜在的兼容性问题与调试难度上升，降低开发效率。

本工具希望借助Openharmony RK3568的基础代码，构建出一套方便开发者修改的成熟代码生成框架。开发者可以在RK3568示例代码的基础上，通过总结文档report中的信息对代码进行相应修改以适配Openharmony操作系统，针对各组合间的差异，采用配置文件与 patch（补丁）的形式进行精细化管理。

### 核心特性

- **自动化代码生成**: 通过最少的配置自动生成完整的设备代码结构
- **模块化架构**: 支持板级（Board）、芯片级（SoC）和厂商级（Vendor）组件
- **模板驱动**: 基于 Jinja2 的灵活模板系统，支持高度定制化
- **多语言支持**: 生成 C/C++、Shell 脚本和配置文件
- **构建系统集成**: 自动生成 BUILD.gn、Makefile 等构建文件
- **自定义代码支持**: 高级自定义代码修改和替换功能
- **Patch系统**: 支持对任意文件进行增量修改的patch功能
- **总结文档**：output下会输出总结文档，开发者可根据提示按需求修改适配。

## 代码整体构成

### 架构概览

```
OpenHarmony device 代码生成工具
├── 核心生成引擎 (Core Generator)
│   ├── 配置解析器 (Configuration Parser)
│   ├── 模板引擎 (Template Engine)
│   └── 文件生成器 (File Generator)
├── 处理器系统 (Processor System)
│   ├── 板级处理器 (Board Processors)
│   ├── 芯片级处理器 (SoC Processors)
│   └── 厂商级处理器 (Vendor Processors)
├── 模板系统 (Template System)
│   ├── 设备模板 (Device Templates)
│   └── 厂商模板 (Vendor Templates)
└── 自定义代码系统 (Custom Code System)
    ├── 代码替换引擎 (Code Replacement Engine)
    ├── Patch处理器 (Patch Processor)
    └── 自定义配置解析器 (Custom Config Parser)
```

### 目录结构

```
device_code_generator/
├── src/                          # 源代码目录
│   └── oh_codegen/              # 主包
│       ├── processors/          # 组件处理器
│       │   ├── board/          # 板级处理器
│       │   ├── soc/            # 芯片级处理器
│       │   └── vendor/         # 厂商级处理器
│       ├── custom_code/        # 自定义代码处理
│       ├── utils/              # 工具函数
│       └── simplified_generator.py  # 主生成器
├── template/                    # 模板文件目录
│   ├── device/                 # 设备模板
│   │   ├── board/             # 板级模板
│   │   └── soc/               # 芯片级模板
│   └── vendor/                # 厂商模板
├── config/                     # 配置文件目录
├── output/                     # 生成代码输出目录
├── docs/                       # 文档目录
└── tests/                      # 测试文件
```

### 三层架构设计

1. **板级（Board）**: 硬件板卡相关的驱动和配置
   - 内核配置、音频驱动、摄像头驱动等
   - 输出路径: `output/{version}/{product}/board/{company}/{product}/`

2. **芯片级（SoC）**: 芯片平台相关的组件
   - GPU、编解码器、显示控制器等
   - 输出路径: `output/{version}/{product}/soc/{soc_company}/{soc}/`

3. **厂商级（Vendor）**: 产品和厂商特定的配置
   - 产品配置、应用配置、安全配置等
   - 输出路径: `output/{version}/{product}/vendor/{company}/{product}/`

## 快速开始

### 环境要求

- Python 3.8 或更高版本
- OpenHarmony 开发环境（本项目基于OpenHarmony v5.0.0 TAG版本开发)

### 安装步骤

1. **克隆仓库**:
```bash
git clone <repository-url>
cd device_code_generator
```

2. **安装依赖**:

   **方法1: 使用pip直接安装（推荐）**
   ```bash
   pip install -e .
   ```

   **方法2: 手动安装依赖**
   ```bash
   pip install PyYAML>=6.0 Jinja2>=3.0.0
   ```

### 基本使用

1. **配置设备信息**: 编辑配置文件（如 `config/rk3568.yaml`）

2. **生成基础代码**:
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml --clean
```

3. **应用patch修改**（新功能）:
```bash
# 查看可用的patch配置
./patch-apply list patches

# 预览将要应用的修改
./patch-apply apply-dir patches rk3568 --dry-run --verbose

# 应用所有patch
./patch-apply apply-dir patches rk3568 --verbose

# 或运行完整演示
./examples/rk3568_adaptation_demo.sh --preview
```

3. **清理并重新生成**:
```bash
python -m src.oh_codegen.simple_cli rk3568.yaml --clean
```

4. **查看生成结果**:
```bash
ls -la output/5.0.0/rk3568/
```

## 🆕 新功能：独立的Patch应用系统

### 核心特性

- **🔧 独立控制** - patch应用从代码生成中分离，提供精确控制
- **📂 分层管理** - 支持按board/soc/vendor层级分别应用patch
- **👀 预览模式** - 干运行模式，安全预览修改内容
- **🛠️ 多种工具** - Python API、CLI工具、Bash脚本等多种使用方式

### 目录结构

```
patches/
├── rk3568/                    # 产品名目录
│   ├── board/                 # board层配置
│   │   └── custom.yaml        # 支持函数替换等复杂修改
│   ├── soc/                   # soc层配置
│   │   └── custom.yaml        # SoC层特定配置
│   └── vendor/                # vendor层配置
│       └── custom.yaml        # 产品定制配置
└── your_product/              # 您的产品
    ├── board/
    ├── soc/
    └── vendor/
```

### 快速体验

```bash
# 运行完整的RK3568适配演示
./examples/rk3568_adaptation_demo.sh --preview

# 查看详细指南
cat examples/rk3568_adaptation_guide.md
```

## 配置说明

### 基本配置结构

```yaml
# 基本设备信息
product_name: "rk3568"           # 产品名称
device_company: "hihope"         # 设备厂商
soc_company: "rockchip"          # 芯片厂商
soc: "rk3568"                    # 芯片型号
ohos_version: "5.0.0"            # OpenHarmony 版本

# 板级模块配置
board_code:
  - kernel:                      # 内核模块
      enabled: true
      linux_kernel_version: "5.10"
      if_use_kernel_patch: true
  - audio_drivers:               # 音频驱动
      enabled: true
      adm_enabled: true
      adm_codec_chip: "rk809"
  - camera_drivers:              # 摄像头驱动
      enabled: true
      nodes: ["exif", "codec"]

# 芯片级组件配置
soc_code:
  - display:                         # GPU 组件
      enabled: true
  - gpu:                         # GPU 组件
      enabled: true
  - codec:                         # 媒体处理平台
      enabled: true
  - mpp:                         # 媒体处理平台
      enabled: true
  - rga:                         # 图形加速器
      enabled: true
  - omx_il:                      # OpenMAX IL
      enabled: true

# 厂商级组件配置
vendor_code:
  - bluetooth:                   # 蓝牙配置
      enabled: true
  - security_config:             # 安全配置
      enabled: true
```

### 高级配置选项

- **模块依赖**: 配置模块间的依赖关系
- **条件编译**: 基于条件的代码生成
- **自定义变量**: 模板中使用的自定义变量

## 输出结构

生成的代码遵循 OpenHarmony 标准目录结构：

```
output/5.0.0/rk3568/
├── board/hihope/rk3568/         # 板级代码
│   ├── kernel/                  # 内核相关
│   ├── audio_drivers/           # 音频驱动
│   ├── camera/                  # 摄像头驱动
│   ├── cfg/                     # 配置文件
│   └── BUILD.gn                 # 构建文件
├── soc/rockchip/rk3568/         # 芯片级代码
│   ├── gpu/                     # GPU 组件
│   │   ├── include/            # 头文件
│   │   ├── lib/                # 32位库
│   │   └── lib64/              # 64位库
│   ├── mpp/                     # 媒体处理平台
│   ├── rga/                     # 图形加速器
│   └── BUILD.gn                # 构建文件
└── vendor/hihope/rk3568/        # 厂商级代码
    ├── bluetooth/               # 蓝牙配置
    ├── security_config/         # 安全配置
    ├── config.json             # 产品配置
    └── BUILD.gn                # 构建文件
```

## 高级功能

### 自定义代码修改

对于高级代码定制和替换功能，请参考我们的详细指南：
**[自定义代码指南](docs/CUSTOM_CODE_GUIDE.md)**

### 🆕 Patch功能

新增的patch功能支持对任意文件进行增量修改，包括BUILD.gn、JSON配置文件等：
**[Patch功能指南](docs/PATCH_FUNCTIONALITY_GUIDE.md)**

#### 快速体验patch功能

```bash

# 运行patch功能测试
python tests/test_patch_functionality.py

# 使用patch生成工具
python -m src.oh_codegen.tools.patch_generator --help
```

### 模板定制

模板文件位于 `template/` 目录，使用 Jinja2 语法进行动态内容生成。支持：
- 条件渲染
- 循环生成
- 变量替换
- 包含和继承

### 处理器扩展

可以通过继承基础处理器类来扩展新的组件处理器：

```python
from ..base_processor import BaseBoardProcessor

class MyCustomProcessor(BaseBoardProcessor):
    def process(self, config, output_dir):
        # 自定义处理逻辑
        pass
```

## 项目配置文件说明

项目使用现代Python包管理方式，包含以下配置文件：

### pyproject.toml（主要配置文件）
- **用途**: 现代Python项目的标准配置文件
- **包含**: 项目元数据、依赖管理、构建配置、开发工具配置
- **依赖**: PyYAML>=6.0, Jinja2>=3.0.0
- **安装**: `pip install -e .`

### setup.py（兼容性支持）
- **用途**: 传统setuptools配置，提供向后兼容
- **功能**: 从setup.cfg读取配置进行包构建
- **使用**: `python setup.py install`（不推荐）

### setup.cfg（传统配置）
- **用途**: 传统配置文件，包含基本包信息
- **内容**: 包名、版本、Python版本要求
- **状态**: 主要用于兼容性，建议使用pyproject.toml

**推荐使用方式**: 优先使用 `pip install -e .` 基于pyproject.toml进行安装

## 开发指南

### 添加新组件

1. 在 `template/device/` 下创建组件模板
2. 在 `src/oh_codegen/processors/` 下创建处理器
3. 在配置文件中注册组件
4. 更新文档和测试

### 调试和日志

生成器提供详细的日志输出，可以通过以下方式查看：

```bash
# 启用详细日志
python -m src.oh_codegen.simple_cli rk3568.yaml --verbose

# 查看生成报告
cat output/5.0.0/rk3568/board/hihope/DEVELOPMENT_GUIDE.md
```

## 常见问题

### Q: 如何添加新的芯片支持？
A: 复制现有的配置文件，修改芯片相关参数，并根据需要调整模板。

### Q: 生成的代码可以直接编译吗？
A: 生成的代码提供了基础框架，可能需要根据具体硬件进行调整。

### Q: 如何自定义模板？
A: 编辑 `template/` 目录下的 `.j2` 文件，使用 Jinja2 语法。

### Q: 如何使用新的patch应用系统？
A: 查看 [Patch应用API和CLI指南](docs/PATCH_API_AND_CLI_GUIDE.md) 和 [RK3568适配示例](examples/rk3568_adaptation_guide.md)。

## 📚 文档

- [Patch目录结构化指南](docs/PATCH_DIRECTORY_STRUCTURE_GUIDE.md) - 详细的patch配置指南
- [Patch应用API和CLI指南](docs/PATCH_API_AND_CLI_GUIDE.md) - API和命令行工具使用
- [RK3568适配完整示例](examples/rk3568_adaptation_guide.md) - 完整的适配流程示例
- [Patch功能指南](docs/PATCH_FUNCTIONALITY_GUIDE.md) - 传统patch功能说明
- [自定义代码指南](docs/CUSTOM_CODE_GUIDE.md) - 自定义代码功能说明

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/xxxxx`)
3. 提交更改 (`git commit -m 'Add some xxxxx'`)
4. 推送到分支 (`git push origin feature/xxxxx`)
5. 开启 Pull Request

